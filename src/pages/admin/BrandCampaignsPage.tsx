import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Typography,
  CircularProgress,
  TableSortLabel,
  Card,
  CardContent,
  useTheme,
  alpha,
} from '@mui/material';
import { Edit as EditIcon, Add as AddIcon } from '@mui/icons-material';
import AdminLayout from '../../components/admin/AdminLayout';

interface BrandCampaign {
  id: number;
  brandId: number;
  campaignId: number;
  isActive: boolean | null;
  createdAt: string | null;
  updatedAt: string | null;
  brand: {
    id: number;
    name: string;
    isActive: boolean;
  };
  campaign: {
    id: number;
    name: string;
    isActive: boolean;
  };
}

type Order = 'asc' | 'desc';

interface HeadCell {
  id: keyof BrandCampaign | 'brand.name' | 'campaign.name' | 'actions';
  numeric: boolean;
  sortable: boolean;
  label: string;
}

export default function BrandCampaignsPage() {
  const theme = useTheme();
  const [brandCampaigns, setBrandCampaigns] = useState<BrandCampaign[]>([]);
  const [filteredBrandCampaigns, setFilteredBrandCampaigns] = useState<BrandCampaign[]>([]);
  const [brands, setBrands] = useState<{ id: number; name: string; isActive: boolean }[]>([]);
  const [campaigns, setCampaigns] = useState<{ id: number; name: string; isActive: boolean }[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedBrandCampaign, setSelectedBrandCampaign] = useState<BrandCampaign | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [order, setOrder] = useState<Order>('asc');
  const [orderBy, setOrderBy] = useState<keyof BrandCampaign | 'brand.name' | 'campaign.name' | 'actions'>('id');

  // Filtreleme state'i
  const [searchText, setSearchText] = useState('');

  const [formData, setFormData] = useState({
    brandId: '',
    campaignId: '',
    isActive: true as boolean | null,
  });

  const [errorDialog, setErrorDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [formErrors, setFormErrors] = useState<{ brandId?: string; campaignId?: string }>({});

  useEffect(() => {
    fetchBrandCampaigns();
    fetchBrands();
    fetchCampaigns();
  }, []);

  // Filtreleme mantığı
  useEffect(() => {
    let filtered = brandCampaigns;
    if (searchText.trim() !== '') {
      const lowercasedFilter = searchText.toLowerCase();
      filtered = brandCampaigns.filter(bc =>
        bc.brand.name.toLowerCase().includes(lowercasedFilter) ||
        bc.campaign.name.toLowerCase().includes(lowercasedFilter)
      );
    }
    setFilteredBrandCampaigns(filtered);
  }, [searchText, brandCampaigns]);

  const fetchBrandCampaigns = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/brand-to-campaign');
      if (response.data && Array.isArray(response.data)) {
        setBrandCampaigns(response.data);
        setFilteredBrandCampaigns(response.data);
      } else {
        setBrandCampaigns([]);
        setFilteredBrandCampaigns([]);
      }
    } catch (_) {
      setBrandCampaigns([]);
      setFilteredBrandCampaigns([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchBrands = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/brand');
      if (response.data && Array.isArray(response.data)) {
        setBrands(response.data);
      } else {
        setBrands([]);
      }
    } catch (_) {
      setBrands([]);
    }
  };

  const fetchCampaigns = async () => {
    try {
      const response = await axios.get('https://360avantajli.com/api/Campaign_Service/campaign');
      if (response.data && Array.isArray(response.data)) {
        setCampaigns(response.data);
      } else {
        setCampaigns([]);
      }
    } catch (_) {
      setCampaigns([]);
    }
  };

  const handleOpenDialog = (brandCampaign?: BrandCampaign) => {
    if (brandCampaign) {
      setSelectedBrandCampaign(brandCampaign);
      setFormData({
        brandId: brandCampaign.brand.id.toString(),
        campaignId: brandCampaign.campaign.id.toString(),
        isActive: brandCampaign.isActive ?? true,
      });
    } else {
      setSelectedBrandCampaign(null);
      setFormData({
        brandId: '',
        campaignId: '',
        isActive: true,
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedBrandCampaign(null);
    setFormData({
      brandId: '',
      campaignId: '',
      isActive: true,
    });
  };

  const validateForm = () => {
    const errors: { brandId?: string; campaignId?: string } = {};
    if (!formData.brandId) {
      errors.brandId = 'Marka seçilmelidir';
    }
    if (!formData.campaignId) {
      errors.campaignId = 'Kampanya seçilmelidir';
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    try {
      setIsLoading(true);
      if (selectedBrandCampaign) {
        await axios.put(`https://360avantajli.com/api/Campaign_Service/brand-to-campaign/${selectedBrandCampaign.id}`, {
          campaignId: parseInt(formData.campaignId),
          brandId: parseInt(formData.brandId),
          isActive: formData.isActive
        });
      } else {
        await axios.post('https://360avantajli.com/api/Campaign_Service/brand-to-campaign', {
          campaignId: parseInt(formData.campaignId),
          brandId: parseInt(formData.brandId),
          isActive: formData.isActive
        });
      }
      fetchBrandCampaigns();
      handleCloseDialog();
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
        setErrorDialog(true);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      setIsLoading(true);
      const brandCampaign = brandCampaigns.find(bc => bc.id === id);
      if (brandCampaign && brandCampaign.brand && brandCampaign.campaign) {
        const requestData = {
          brandId: brandCampaign.brand.id.toString(),
          campaignId: brandCampaign.campaign.id.toString(),
          isActive: !brandCampaign.isActive
        };

        await axios.put(
          `https://360avantajli.com/api/Campaign_Service/brand-to-campaign/${id}`,
          requestData
        );

        await fetchBrandCampaigns();
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setErrorMessage(error.response.data.message);
        setErrorDialog(true);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string | null): string => {
    if (!dateString) {
      return 'N/A';
    }

    // Check for "DD-MM-YYYY HH:mm:ss" format
    const customFormatMatch = dateString.match(/^(\d{2})-(\d{2})-(\d{4})\s(\d{2}):(\d{2}):(\d{2})$/);
    let date: Date;

    if (customFormatMatch) {
      const [, day, month, year, hours, minutes, seconds] = customFormatMatch;
      date = new Date(Number(year), Number(month) - 1, Number(day), Number(hours), Number(minutes), Number(seconds));
    } else {
      // Fallback to direct parsing for ISO 8601 and other standard formats
      date = new Date(dateString);
    }

    if (isNaN(date.getTime())) {
      return 'N/A';
    }

    return date.toLocaleString('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  function descendingComparator<T>(a: T, b: T, orderBy: keyof T | 'brand.name' | 'campaign.name' | 'actions') {
    if (orderBy === 'brand.name') {
      return (b as any).brand.name.localeCompare((a as any).brand.name);
    }
    if (orderBy === 'campaign.name') {
      return (b as any).campaign.name.localeCompare((a as any).campaign.name);
    }
    if (orderBy === 'actions') {
      return 0;
    }
    if ((b[orderBy] as any) < (a[orderBy] as any)) {
      return -1;
    }
    if ((b[orderBy] as any) > (a[orderBy] as any)) {
      return 1;
    }
    return 0;
  }

  function getComparator<Key extends keyof BrandCampaign | 'brand.name' | 'campaign.name' | 'actions'>(
    order: Order,
    orderBy: Key,
  ): (a: BrandCampaign, b: BrandCampaign) => number {
    return order === 'desc'
      ? (a, b) => descendingComparator(a, b, orderBy)
      : (a, b) => -descendingComparator(a, b, orderBy);
  }

  function stableSort<T>(array: readonly T[], comparator: (a: T, b: T) => number) {
    const stabilizedThis = array.map((el, index) => [el, index] as [T, number]);
    stabilizedThis.sort((a, b) => {
      const order = comparator(a[0], b[0]);
      if (order !== 0) {
        return order;
      }
      return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
  }

  const handleRequestSort = (property: keyof BrandCampaign | 'brand.name' | 'campaign.name' | 'actions') => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const headCells: readonly HeadCell[] = [
    { id: 'id', numeric: true, sortable: true, label: 'ID' },
    { id: 'brand.name', numeric: false, sortable: true, label: 'Marka' },
    { id: 'campaign.name', numeric: false, sortable: true, label: 'Kampanya' },
    { id: 'isActive', numeric: false, sortable: true, label: 'Durum' },
    { id: 'createdAt', numeric: false, sortable: true, label: 'Oluşturulma' },
    { id: 'actions', numeric: false, sortable: false, label: 'İşlemler' },
  ];

  const getStatusBackgroundColor = (isActive: boolean | null) => {
    if (isActive) {
      return alpha(theme.palette.success.main, 0.15);
    }
    return alpha(theme.palette.error.main, 0.12);
  };

  return (
    <AdminLayout>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h1">
          Marka-Kampanya İlişkileri
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          disabled={isLoading}
        >
          Yeni İlişki Ekle
        </Button>
      </Box>

      <Paper sx={{ mb: 2, p: 2 }}>
        <TextField
          fullWidth
          label="Marka veya Kampanya Ara"
          variant="outlined"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
        />
      </Paper>

      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : filteredBrandCampaigns.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            İlişki bulunamadı.
          </Typography>
        </Paper>
      ) : (
        <>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Toplam {brandCampaigns.length} ilişki içinden {filteredBrandCampaigns.length} ilişki gösteriliyor.
            </Typography>
          </Box>
          <TableContainer component={Paper}>
            <Table stickyHeader aria-label="sticky table">
              <TableHead>
                <TableRow
                  sx={{
                    '& .MuiTableCell-head': {
                      fontWeight: 'bold',
                      backgroundColor: theme.palette.grey[100],
                    }
                  }}
                >
                  {headCells.map((headCell) => (
                    <TableCell
                      key={headCell.id.toString()}
                      align={headCell.numeric ? 'right' : 'left'}
                      sortDirection={orderBy === headCell.id ? order : false}
                    >
                      {headCell.sortable ? (
                        <TableSortLabel
                          active={orderBy === headCell.id}
                          direction={orderBy === headCell.id ? order : 'asc'}
                          onClick={() => handleRequestSort(headCell.id)}
                        >
                          {headCell.label}
                        </TableSortLabel>
                      ) : (
                        headCell.label
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {stableSort(filteredBrandCampaigns, getComparator(order, orderBy)).map((bc) => (
                  <TableRow
                    key={bc.id}
                    sx={{
                      backgroundColor: getStatusBackgroundColor(bc.isActive),
                      '&:hover': {
                        filter: 'brightness(0.95)',
                      },
                      '&:last-child td, &:last-child th': {
                        border: 0,
                      },
                    }}
                  >
                    <TableCell align="right">{bc.id}</TableCell>
                    <TableCell>
                      <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                        {bc.brand?.name || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>{bc.campaign?.name || 'N/A'}</TableCell>
                    <TableCell>
                      <Switch
                        checked={bc.isActive ?? false}
                        onChange={() => handleToggleActive(bc.id)}
                        color={bc.isActive ? 'success' : 'error'}
                      />
                    </TableCell>
                    <TableCell>{formatDate(bc.createdAt)}</TableCell>
                    <TableCell>
                      <IconButton onClick={() => handleOpenDialog(bc)}>
                        <EditIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>{selectedBrandCampaign ? 'İlişkiyi Düzenle' : 'Yeni Marka-Kampanya İlişkisi'}</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <FormControl fullWidth sx={{ mb: 2 }} error={!!formErrors.brandId}>
              <InputLabel>Marka</InputLabel>
              <Select
                value={formData.brandId}
                label="Marka"
                onChange={(e) => setFormData({ ...formData, brandId: e.target.value })}
                required
                disabled={isLoading}
              >
                {brands
                  .filter(brand => brand.isActive)
                  .map((brand) => (
                    <MenuItem key={brand.id} value={brand.id}>
                      {brand.name}
                    </MenuItem>
                  ))}
              </Select>
              {formErrors.brandId && (
                <Typography variant="caption" color="error">{formErrors.brandId}</Typography>
              )}
            </FormControl>
            <FormControl fullWidth sx={{ mb: 2 }} error={!!formErrors.campaignId}>
              <InputLabel>Kampanya</InputLabel>
              <Select
                value={formData.campaignId}
                label="Kampanya"
                onChange={(e) => setFormData({ ...formData, campaignId: e.target.value })}
                required
                disabled={isLoading}
              >
                {campaigns
                  .filter(campaign => campaign.isActive)
                  .map((campaign) => (
                    <MenuItem key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </MenuItem>
                  ))}
              </Select>
              {formErrors.campaignId && (
                <Typography variant="caption" color="error">{formErrors.campaignId}</Typography>
              )}
            </FormControl>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive ?? false}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                  disabled={isLoading}
                />
              }
              label="Aktif"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={isLoading}>
            İptal
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isLoading || !formData.brandId || !formData.campaignId}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {selectedBrandCampaign ? 'Güncelle' : 'Ekle'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Hata Dialog */}
      <Dialog
        open={errorDialog}
        onClose={() => setErrorDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: 'transparent',
            boxShadow: 'none',
          }
        }}
      >
        <Box
          sx={{
            bgcolor: '#fff',
            borderRadius: 3,
            boxShadow: 6,
            p: 0,
            position: 'relative',
          }}
        >
          <Card
            sx={{
              bgcolor: '#ffeaea',
              border: '1px solid #ffb4b4',
              borderRadius: 3,
              boxShadow: 3,
              mx: 3,
              mt: 3,
              mb: 0,
              p: 0,
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ color: '#d32f2f', fontWeight: 700, mb: 1 }}>
                Hata
              </Typography>
              <Typography variant="body1" sx={{ color: '#b71c1c', fontSize: 17, fontWeight: 500 }}>
                {errorMessage}
              </Typography>
            </CardContent>
          </Card>
          <DialogActions sx={{ justifyContent: 'flex-end', px: 3, pb: 2, pt: 1 }}>
            <Button
              onClick={() => setErrorDialog(false)}
              variant="contained"
              sx={{
                bgcolor: '#d32f2f',
                color: '#fff',
                fontWeight: 600,
                borderRadius: 2,
                px: 4,
                py: 1.2,
                boxShadow: 2,
                '&:hover': { bgcolor: '#b71c1c' }
              }}
            >
              Tamam
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </AdminLayout>
  );
}