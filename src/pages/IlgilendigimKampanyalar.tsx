import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import api from '../utils/api';
import { format, isValid } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Box, Paper, Typography, useTheme, CircularProgress } from '@mui/material';
import { getDaysLeft, parseDate } from '../utils/dateUtils';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import { createSlug as slugifyTurkish } from '../utils/urlUtils';
import { useTestMode } from '../components/common/TestModeWrapper';

interface CampaignForm {
  id: number;
  form: {
    id: number;
    name: string;
    surname: string;
    email: string;
    phoneNumber: string;
    country: string;
    city: string;
    town: string;
    gender: string;
    birthday: string | null;
    isActive: boolean;
    createdAt: string;
    updatedAt: string | null;
  };
  campaign: {
    id: number;
    category: {
      id: number;
      name: string;
      isActive: boolean;
      campaignDetail: {
        id: number;
        details: Record<string, any>;
        isActive: boolean;
        createdAt: string;
        updatedAt: string | null;
      };
      parentCategoryId: number;
      createdAt: string;
      updatedAt: string | null;
    };
    name: string;
    title: string;
    description: string;
    details: Record<string, any>;
    startDate: string;
    endDate: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string | null;
    brand?: {
      id: number;
      name: string;
      logo?: string;
      logoUrl?: string;
      brandUrl?: string;
    };
    imageUrl?: string;
  };
  ipAddress: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string | null;
}

const MyCampaigns: React.FC = () => {
  const [campaignForms, setCampaignForms] = useState<CampaignForm[]>([]);
  const [customerId, setCustomerId] = useState<number | null>(null);
  const [brandCampaigns, setBrandCampaigns] = useState<any[]>([]); // brand-to-campaign datası
  const [isLoading, setIsLoading] = useState(true); // Loading state eklendi
  const { user } = useAuth();
  const theme = useTheme();
  const intl = useIntl();
  const stdNavigate = useNavigate(); // Renamed standard navigate
  const { navigate: testNavigate, isTestMode } = useTestMode(); // Use test mode hook
  const navigate = isTestMode ? testNavigate : stdNavigate; // Set navigate based on test mode

  // Tarih formatlama yardımcı fonksiyonu
  const formatDate = (dateString: string | null, formatStr: string = 'dd MMMM yyyy') => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return isValid(date) ? format(date, formatStr, { locale: tr }) : '-';
  };

  // Marka-marka eşleşmelerini çek
  useEffect(() => {
    const fetchBrandCampaigns = async () => {
      try {
        const response = await api.get('/api/Campaign_Service/brand-to-campaign');
        setBrandCampaigns(response.data);
      } catch (error) {
        setBrandCampaigns([]);
      }
    };
    fetchBrandCampaigns();
  }, []);

  // Kullanıcı bilgilerini al ve customerId'yi bul
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await api.get('/api/Auth_Service/auth/current-user');
        if (response.data && response.data.username) {
          // Şimdi customerId'yi bul
          const customerResponse = await api.get('/api/Campaign_Service/customer');
          if (customerResponse.data && Array.isArray(customerResponse.data)) {
            const customer = customerResponse.data.find((c: any) => c.username === response.data.username || c.email === response.data.username);
            if (customer) {
              setCustomerId(customer.id);
            }
          }
        }
      } catch (error) {
        console.error('Kullanıcı bilgileri alınırken hata oluştu:', error);
      }
    };
    if (user) {
      fetchUserData();
    }
  }, [user]);

  // Kampanya formlarını al ve marka bilgisini ekle
  useEffect(() => {
    const fetchCampaignForms = async () => {
      setIsLoading(true); // Veri çekilmeye başlarken true yap
      try {
        const response = await api.get('/api/Campaign_Service/customer-to-campaign');
        const data = response.data;
        // Kullanıcının customerId'sine göre filtreleme
        let filteredForms = data.filter((item: any) => item.customer?.id === customerId);
        // Her kampanya için ilgili marka bilgisini ekle
        filteredForms = filteredForms.map((item: any) => {
          const brandCampaign = brandCampaigns.find(
            (bc) => bc.campaign && bc.campaign.id === item.campaign.id && bc.isActive
          );
          let campaign = item.campaign;
          if (brandCampaign && brandCampaign.brand) {
            campaign = {
              ...campaign,
              brand: {
                id: brandCampaign.brand.id,
                name: brandCampaign.brand.name,
                logo: brandCampaign.brand.logo,
                logoUrl: brandCampaign.brand.logoUrl,
                brandUrl: brandCampaign.brand.brandUrl,
              },
            };
          }
          // Eskiyle uyumlu olması için form alanı ekle
          return {
            id: item.id,
            form: {}, // form alanı boş, gerekirse customer datası eklenebilir
            campaign,
            ipAddress: null,
            isActive: item.isActive,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
          };
        });
        setCampaignForms(filteredForms);
      } catch (error) {
        console.error('Kampanya formları yüklenirken hata oluştu:', error);
        setCampaignForms([]); // Hata durumunda boş array set et
      } finally {
        setIsLoading(false); // Veri çekme bitince (başarılı veya hatalı) false yap
      }
    };
    if (customerId && brandCampaigns.length > 0) {
      fetchCampaignForms();
    }
  }, [customerId, brandCampaigns]);

  // Ana görsel endpointi
  const getShowcaseImageUrl = (campaign: CampaignForm['campaign']) => {
    if (campaign.imageUrl) return campaign.imageUrl;
    if (!campaign.id) return '';
    return `https://360avantajli.com/api/Campaign_Service/campaign-image/showcase/${campaign.id}`;
  };

  // Marka logo url (Home ile aynı mantık)
  const getBrandLogoUrl = (campaign: CampaignForm['campaign']) => {
    if (campaign.brand && campaign.brand.id) {
      return `https://360avantajli.com/api/Campaign_Service/brand/${campaign.brand.id}/image`;
    }
    if (campaign.brand && campaign.brand.logoUrl) return campaign.brand.logoUrl;
    if (campaign.brand && campaign.brand.logo) return campaign.brand.logo;
    return '/placeholder-logo.jpg';
  };

  // Marka adı
  const getBrandName = (campaign: CampaignForm['campaign']) => {
    if (campaign.brand && campaign.brand.name) return campaign.brand.name;
    return campaign.category?.name || '';
  };

  return (
    <Box sx={{ background: '#f7f9fb', minHeight: 'calc(100vh - 60px)', py: 6, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
      <Typography variant="h4" sx={{ fontWeight: 700, mb: 6, textAlign: 'center' }}>
        İlgilendiğim Kampanyalar
      </Typography>
      <Box sx={{ maxWidth: 1400, mx: 'auto', width: '100%', px: 2 }}>
        {isLoading ? ( // isLoading true ise loading göster
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <CircularProgress />
          </Box>
        ) : campaignForms.length === 0 ? ( // isLoading false ve campaignForms boşsa mesajı göster
          <Typography align="center" color="text.secondary">
            Henüz ilgilendiğiniz bir kampanya bulunmamaktadır.
          </Typography>
        ) : ( // isLoading false ve campaignForms doluysa kartları göster
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr', lg: '1fr 1fr 1fr 1fr' }, gap: 4 }}>
            {campaignForms.map((form) => {
              // Kampanya bitiş süresini hesapla
              const endDateObj = parseDate(form.campaign.endDate);
              const now = new Date();
              const hoursLeft = endDateObj ? Math.floor((endDateObj.getTime() - now.getTime()) / (1000 * 60 * 60)) : -1;
              const daysLeft = getDaysLeft(form.campaign.endDate);

              // 24 saatten az kalan veya bugün biten kampanyalar için kırmızı çerçeve
              const endingVeryVerySoon = hoursLeft >= 0 && hoursLeft < 24;
              const endsToday = daysLeft === 0;

              // Renk belirleme
              let borderColor = '#fff';
              let kalanGunText = '';
              let kalanGunColor = theme.palette.text.secondary;

              if (daysLeft < 0) {
                borderColor = '#9e9e9e';
                kalanGunText = 'Sonlanmış';
                kalanGunColor = '#9e9e9e';
              } else if (daysLeft === 0) {
                borderColor = '#f44336';
                kalanGunText = 'Son gün';
                kalanGunColor = '#f44336';
              } else if (daysLeft <= 3) {
                borderColor = '#f44336';
                kalanGunText = `${daysLeft} gün kaldı`;
                kalanGunColor = '#f44336';
              } else {
                kalanGunText = `${daysLeft} gün kaldı`;
              }

              return (
                <Paper
                  key={form.id}
                  elevation={3}
                  onClick={() => {
                    const slug = slugifyTurkish(form.campaign.name);
                    navigate(`/campaigns/${slug}`); // Navigate now correctly uses test or standard path
                  }}
                  sx={{
                    borderRadius: 4,
                    overflow: 'hidden',
                    boxShadow: '0 4px 24px 0 rgba(0,0,0,0.06)',
                    border: `2.5px solid ${borderColor}`,
                    background: '#fff',
                    transition: 'transform 0.2s, box-shadow 0.3s',
                    '&:hover': {
                      transform: 'translateY(-6px)',
                      boxShadow: '0 8px 32px 0 rgba(0,0,0,0.10)',
                    },
                    display: 'flex',
                    flexDirection: 'column',
                    minHeight: 390,
                    position: 'relative', // logo için gerekli
                  }}
                >
                  {/* Marka logo (Home ile aynı) */}
                  <Box
                    onClick={(e) => {
                      e.stopPropagation();
                      const brandName = getBrandName(form.campaign).toLowerCase().replace(/\s+/g, '-');
                      navigate(`/test/brands/${brandName}`);
                    }}
                    sx={{
                      position: 'absolute',
                      top: 12,
                      left: 12,
                      display: 'flex',
                      alignItems: 'center',
                      zIndex: 2,
                      cursor: 'pointer',
                      maxWidth: '70%',
                    }}
                  >
                    <Box
                      sx={{
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        backgroundColor: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: '4px',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
                        transition: 'all 0.2s ease',
                        flexShrink: 0,
                        '&:hover': {
                          transform: 'scale(1.1)',
                          boxShadow: '0 6px 16px rgba(0,0,0,0.25)',
                        }
                      }}
                    >
                      <img
                        src={getBrandLogoUrl(form.campaign)}
                        alt={getBrandName(form.campaign) + ' logo'}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'contain',
                          borderRadius: '50%',
                        }}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.onerror = null;
                          target.src = '/placeholder-logo.jpg';
                        }}
                      />
                    </Box>
                  </Box>
                  {/* Kampanya görseli */}
                  <Box
                    component="img"
                    src={getShowcaseImageUrl(form.campaign)}
                    alt={form.campaign.name}
                    sx={{
                      width: '100%',
                      height: 170,
                      objectFit: 'cover',
                      objectPosition: 'center',
                      borderTopLeftRadius: 16,
                      borderTopRightRadius: 16,
                    }}
                  />
                  {/* İçerik */}
                  <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', p: 2, pt: 3 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 700, mb: 1, lineHeight: 1.2, display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical', overflow: 'hidden' }}>
                      {form.campaign.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2, minHeight: 40, display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical', overflow: 'hidden' }}>
                      {form.campaign.description || form.campaign.title}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 'auto' }}>
                      <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                        {getBrandName(form.campaign)}
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 500, color: kalanGunColor }}>
                        {kalanGunText}
                      </Typography>
                    </Box>
                  </Box>
                </Paper>
              );
            })}
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default MyCampaigns; 