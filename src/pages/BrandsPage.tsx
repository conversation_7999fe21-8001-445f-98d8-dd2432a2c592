import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTestMode } from '../components/common/TestModeWrapper';
import { useIntl } from 'react-intl';
import axios from 'axios';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CircularProgress,
  Breadcrumbs,
  Link,
  useTheme,
  CardActionArea,
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { NavigateNext as NavigateNextIcon } from '@mui/icons-material';

interface Brand {
  id: number;
  name: string;
  logo?: string;
  logoUrl?: string;
  brandUrl?: string;
  isActive?: boolean;
  imagePath?: string;
  [key: string]: unknown;
}

interface Category {
  id: number;
  name: string;
  isActive: boolean;
  parentCategoryId: number | null;
}

interface BrandToCategory {
  id: number;
  brand: Brand;
  category: Category;
  isActive: boolean;
}

const BrandsPage: React.FC = () => {
  const standardNavigate = useNavigate();
  const { navigate: testNavigate, isTestMode } = useTestMode();
  const navigate = isTestMode ? testNavigate : standardNavigate;
  const intl = useIntl();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  const [categories, setCategories] = useState<Category[]>([]);
  const [brandToCategories, setBrandToCategories] = useState<BrandToCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Logo işleme için yardımcı fonksiyon
  const renderLogo = (brandId?: number, fallbackUrl?: string) => {
    if (brandId) {
      return `https://360avantajli.com/api/Campaign_Service/brand/${brandId}/image`;
    }
    if (fallbackUrl) return fallbackUrl;
    return '/placeholder-logo.jpg';
  };

  useEffect(() => {
    const fetchAll = async () => {
      setLoading(true);
      setError(null);
      try {
        const [catRes, btcRes] = await Promise.all([
          axios.get('https://360avantajli.com/api/Campaign_Service/category'),
          axios.get('https://360avantajli.com/api/Campaign_Service/brand-to-category'),
        ]);
        if (catRes.data && Array.isArray(catRes.data) && btcRes.data && Array.isArray(btcRes.data)) {
          setCategories(catRes.data.filter((c: Category) => c.isActive));
          setBrandToCategories(btcRes.data.filter((b: BrandToCategory) => b.isActive && b.brand.isActive));
        } else {
          setCategories([]);
          setBrandToCategories([]);
          setError('Veri alınamadı.');
        }
      } catch (err) {
        setError('Veriler yüklenirken hata oluştu.');
        setCategories([]);
        setBrandToCategories([]);
      } finally {
        setLoading(false);
      }
    };
    fetchAll();
  }, []);

  // Kategorileri hiyerarşik olarak gruplama
  const anaKategoriler = categories.filter((cat) => !cat.parentCategoryId || cat.parentCategoryId === 0);
  const altKategoriler = categories.filter((cat) => cat.parentCategoryId && cat.parentCategoryId !== 0);

  // Alt kategorileri ana kategoriye göre grupla
  const getAltKategoriler = (anaKategoriId: number) =>
    altKategoriler.filter((cat) => cat.parentCategoryId === anaKategoriId);

  // Bir kategoriye ait markaları getir
  const getMarkalarByKategori = (kategoriId: number) =>
    brandToCategories.filter((btc) => btc.category.id === kategoriId).map((btc) => btc.brand);

  const TestModeLink = isTestMode
    ? ({ to, ...props }: { to: string; [key: string]: any }) => <RouterLink to={`/test${to}`} {...props} />
    : RouterLink;

  // Ortak BrandCard bileşeni
  const BrandCard = ({ brand, onClick }: { brand: Brand; onClick: () => void }) => (
    <Card
      sx={{
        minHeight: 200,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 3,
        boxShadow: '0 2px 12px 0 rgba(60,72,100,0.10)',
        border: 'none',
        transition: 'transform 0.2s, box-shadow 0.2s',
        cursor: 'pointer',
        '&:hover': {
          transform: 'translateY(-6px) scale(1.04)',
          boxShadow: `0 8px 30px rgba(60,72,100,0.18)`,
        },
      }}
    >
      <CardActionArea
        sx={{
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          p: 2,
        }}
        onClick={onClick}
      >
        <Box sx={{
          width: 80,
          height: 80,
          borderRadius: '50%',
          backgroundColor: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          mb: 2,
          boxShadow: `0 2px 8px rgba(60,72,100,0.07)`,
          border: `1px solid #ececec`,
          overflow: 'hidden',
        }}>
          <img
            src={renderLogo(brand.id, brand.logo || brand.logoUrl)}
            alt={`${brand.name} logo`}
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain',
            }}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.onerror = null;
              target.src = '/placeholder-logo.jpg';
            }}
          />
        </Box>
        <Typography
          variant="subtitle1"
          component="h2"
          sx={{
            fontWeight: 700,
            fontSize: '1rem',
            color: 'text.primary',
            textAlign: 'center',
          }}
        >
          {brand.name}
        </Typography>
      </CardActionArea>
    </Card>
  );

  return (
    <Box sx={{ py: 4, background: '#f7f8fa', minHeight: '100vh' }}>
      <Container>
        {/* Breadcrumbs */}
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          sx={{ mb: 3 }}
        >
          <Link
            component={TestModeLink}
            to="/"
            color="inherit"
            sx={{
              textDecoration: 'none',
              '&:hover': {
                textDecoration: 'underline',
              },
            }}
          >
            Ana Sayfa
          </Link>
          <Typography color="text.primary">
            Markalar
          </Typography>
        </Breadcrumbs>

        {/* Page Title */}
        <Typography
          variant="h4"
          component="h1"
          gutterBottom
          sx={{
            mb: 4,
            fontWeight: 800,
            color: 'primary.main',
            letterSpacing: 1,
          }}
        >
          Markalar
        </Typography>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress size={40} />
          </Box>
        ) : error ? (
          <Typography color="error" align="center" sx={{ py: 2 }}>
            {error}
          </Typography>
        ) : (
          <Box>
            {anaKategoriler.map((anaKategori) => {
              // Alt kategorilerden en az birinde marka varsa veya ana kategorinin kendisinde marka varsa göster
              const altKats = getAltKategoriler(anaKategori.id);
              const anaKategoriMarkalar = getMarkalarByKategori(anaKategori.id);
              const altKategorilerVeMarkalar = altKats
                .map((altKategori) => ({
                  altKategori,
                  markalar: getMarkalarByKategori(altKategori.id)
                }))
                .filter(({ markalar }) => markalar.length > 0);
              if (altKategorilerVeMarkalar.length === 0 && anaKategoriMarkalar.length === 0) return null;
              return (
                <Box key={anaKategori.id} sx={{ mb: 8 }}>
                  {/* Ana kategori başlığı */}
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ width: 6, height: 36, bgcolor: 'primary.main', borderRadius: 2, mr: 2 }} />
                    <Typography variant="h5" sx={{ fontWeight: 800, fontSize: 28, color: 'primary.main', letterSpacing: 1 }}>
                      {anaKategori.name}
                    </Typography>
                  </Box>
                  {/* Alt kategoriler */}
                  {altKategorilerVeMarkalar.length > 0 ? (
                    altKategorilerVeMarkalar.map(({ altKategori, markalar }) => (
                      <Box key={altKategori.id} sx={{ mb: 5, ml: 4 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 700, fontSize: 20, mb: 1, color: 'text.secondary', ml: 0 }}>
                          {altKategori.name}
                        </Typography>
                        <Box sx={{ borderBottom: '1px solid #ececec', mb: 2, width: '100%' }} />
                        <Grid container spacing={4} justifyContent="flex-start" sx={{ ml: 0 }}>
                          {markalar.map((brand) => (
                            <Grid item xs={12} sm={6} md={4} lg={3} key={brand.id}>
                              <BrandCard
                                brand={brand}
                                onClick={() => {
                                  let brandSlug: string;
                                  if (brand.name) {
                                    brandSlug = brand.name.toLowerCase().replace(/\s+/g, '-');
                                  } else if (brand.brandUrl && !brand.brandUrl.includes('://')) {
                                    brandSlug = brand.brandUrl;
                                  } else {
                                    brandSlug = brand.id.toString();
                                  }
                                  navigate(`/test/brands/${brandSlug}`);
                                }}
                              />
                            </Grid>
                          ))}
                        </Grid>
                      </Box>
                    ))
                  ) : (
                    // Eğer alt kategori yoksa, ana kategoriye bağlı markaları göster
                    (() => {
                      if (anaKategoriMarkalar.length === 0) return null;
                      return (
                        <Grid container spacing={4} justifyContent="flex-start" sx={{ ml: 4 }}>
                          {anaKategoriMarkalar.map((brand) => (
                            <Grid item xs={12} sm={6} md={4} lg={3} key={brand.id}>
                              <BrandCard
                                brand={brand}
                                onClick={() => {
                                  let brandSlug: string;
                                  if (brand.name) {
                                    brandSlug = brand.name.toLowerCase().replace(/\s+/g, '-');
                                  } else if (brand.brandUrl && !brand.brandUrl.includes('://')) {
                                    brandSlug = brand.brandUrl;
                                  } else {
                                    brandSlug = brand.id.toString();
                                  }
                                  navigate(`/test/brands/${brandSlug}`);
                                }}
                              />
                            </Grid>
                          ))}
                        </Grid>
                      );
                    })()
                  )}
                </Box>
              );
            })}
          </Box>
        )}
      </Container>
    </Box>
  );
};

export default BrandsPage;
