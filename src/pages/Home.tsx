import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTestMode } from '../components/common/TestModeWrapper';
import { standardizeCampaignEndDate, isExpired, sortCampaignsByEndDate, getDaysLeft, isNewlyAdded, isEndingSoonInWeek, getTimeAgo, getTimeRemaining } from '../utils/dateUtils';
import {
  Container,
  Typography,
  Box,
  Button,
  CircularProgress,
  Card,
} from '@mui/material';
import {
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
} from '@mui/icons-material';
import { useIntl } from 'react-intl';
import Slider from 'react-slick';
import { useTheme } from '@mui/material/styles';
import { CustomSlide } from '../components/features/campaign/CustomSlide';
import { StatsSection } from '../components/features/stats/StatsSection';
import { NewsletterSection } from '../components/features/newsletter/NewsletterSection';
import useApiCache from '../hooks/useApiCache';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { getOrCreateUniqueId } from '../utils/cookieConsentUtils';
import axios from 'axios';
import { createSlug } from '../utils/urlUtils';

// Types
interface CampaignDetailField {
  type: 'text' | 'radio' | 'checkbox';
  label: string;
  value: string | boolean;
  options?: string[];
  required?: boolean;
}

interface Campaign {
  id: string;
  title: string;
  brand: string;
  description: string;
  imageUrl: string;
  logoUrl: string;
  brandUrl: string;
  brandId?: string;
  endDate: string;
  discount: string;
  category: string;
  parentCategoryId?: string;
  categoryName?: string;
  createdAt?: string;
  features: {
    price: string;
    monthlyPayment: string;
    term: string;
    downPayment: string;
    interestRate: string;
  };
  details?: Record<string, CampaignDetailField>;
}

interface Brand {
  id: number;
  name: string;
  countryCode: number;
  brandUrl: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string | null;
  logo?: string;
  logoUrl?: string;
  imagePath?: string;
}

// Custom Arrow Components
const NextArrow = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div
      className={className}
      style={{
        ...style,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        right: '-15px',
        zIndex: 1,
        width: '40px',
        height: '40px',
        borderRadius: '50%',
        backgroundColor: 'rgba(25, 118, 210, 0.8)',
        color: 'white',
        '&:hover': {
          backgroundColor: 'rgba(25, 118, 210, 1)'
        }
      }}
      onClick={onClick}
    >
      <NavigateNextIcon style={{ color: 'white', fontSize: '24px' }} />
    </div>
  );
};

const PrevArrow = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div
      className={className}
      style={{
        ...style,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        left: '-15px',
        zIndex: 1,
        width: '40px',
        height: '40px',
        borderRadius: '50%',
        backgroundColor: 'rgba(25, 118, 210, 0.8)',
        color: 'white',
        '&:hover': {
          backgroundColor: 'rgba(25, 118, 210, 1)'
        }
      }}
      onClick={onClick}
    >
      <NavigateBeforeIcon style={{ color: 'white', fontSize: '24px' }} />
    </div>
  );
};

// Constants
const SLIDER_SETTINGS = {
  dots: false,
  infinite: true,
  speed: 500,
  slidesToShow: 3,
  slidesToScroll: 1,
  autoplay: true,
  autoplaySpeed: 3000,
  swipeToSlide: true,
  draggable: true,
  arrows: true,
  nextArrow: <NextArrow />,
  prevArrow: <PrevArrow />,
  responsive: [
    {
      breakpoint: 1280, // xl breakpoint
      settings: {
        slidesToShow: 3,
        slidesToScroll: 1,
        arrows: true,
      }
    },
    {
      breakpoint: 960, // md breakpoint
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
        arrows: true,
      }
    },
    {
      breakpoint: 600, // sm breakpoint
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
        dots: true,
        centerMode: true,
        centerPadding: '20px',
      }
    },
    {
      breakpoint: 480, // xs breakpoint
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
        dots: true,
        centerMode: false,
        centerPadding: '0px',
      }
    }
  ]
};

function sortBrandsByPopularity(brands: Brand[], popularIds: number[]): Brand[] {
  if (!popularIds || popularIds.length === 0) return brands;
  const idSet = new Set(popularIds);
  const popular = brands.filter(b => idSet.has(b.id));
  const rest = brands.filter(b => !idSet.has(b.id));
  // Popüler id'lerin sırası önemli
  popular.sort((a, b) => popularIds.indexOf(a.id) - popularIds.indexOf(b.id));
  return [...popular, ...rest];
}

function sortCampaignsByPopularity(campaigns: Campaign[], popularIds: string[]): Campaign[] {
  if (!popularIds || popularIds.length === 0) return campaigns;
  const idSet = new Set(popularIds);
  const popular = campaigns.filter(c => idSet.has(c.id));
  const rest = campaigns.filter(c => !idSet.has(c.id));
  // Popüler id'lerin sırası önemli
  popular.sort((a, b) => popularIds.indexOf(a.id) - popularIds.indexOf(b.id));
  return [...popular, ...rest];
}

const Home: React.FC = () => {
  const standardNavigate = useNavigate();
  const { navigate: testNavigate, isTestMode } = useTestMode();
  // Test modunda isek test navigate'i kullan, değilse standart navigate'i kullan
  const navigate = isTestMode ? testNavigate : standardNavigate;

  const theme = useTheme();
  const intl = useIntl();

  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [campaignsLoading, setCampaignsLoading] = useState(true);
  const [brandsLoading, setBrandsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Sürükle özelliği için state'ler
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  // Sürükle event handler'ları
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(true);
    setStartX(e.pageX - (e.currentTarget.offsetLeft || 0));
    setScrollLeft(e.currentTarget.scrollLeft);
    e.currentTarget.style.cursor = 'grabbing';
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(false);
    e.currentTarget.style.cursor = 'grab';
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(false);
    e.currentTarget.style.cursor = 'grab';
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDragging) return;
    e.preventDefault();
    const x = e.pageX - (e.currentTarget.offsetLeft || 0);
    const walk = (x - startX) * 2; // Scroll hızını ayarla
    e.currentTarget.scrollLeft = scrollLeft - walk;
  };

  // Slider stillerini ekle
  useEffect(() => {
    const sliderStyles = `
      .slick-slider {
        position: relative;
        display: block;
        box-sizing: border-box;
        user-select: none;
        touch-action: pan-y;
      }

      .slick-list {
        position: relative;
        display: block;
        overflow: hidden;
        margin: 0;
        padding: 0;
        text-align: left !important;
      }

      .slick-track {
        display: flex;
        justify-content: flex-start !important;
      }

      .slick-slide {
        padding: 0 10px;
      }

      .slick-arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: rgba(25, 118, 210, 0.8);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        display: flex !important;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: white;
        opacity: 0.9;
      }

      .slick-arrow:hover {
        background-color: rgba(25, 118, 210, 1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        transform: translateY(-50%) scale(1.1);
        opacity: 1;
      }

      .slick-arrow.slick-prev {
        left: -15px;
      }

      .slick-arrow.slick-next {
        right: -15px;
      }
      
      .slick-arrow::before {
        display: none;
      }

      .slick-dots {
        bottom: -40px;
        text-align: center;
      }

      .slick-dots li {
        margin: 0 4px;
      }

      .slick-dots li button {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.3);
        border: none;
        padding: 0;
        font-size: 0;
        transition: all 0.3s ease;
      }

      .slick-dots li.slick-active button {
        background-color: #4A90E2;
        transform: scale(1.2);
      }

      /* Responsive adjustments */
      @media (max-width: 960px) {
        .slick-arrow {
          width: 36px;
          height: 36px;
        }

        .slick-arrow.slick-prev {
          left: -15px;
        }

        .slick-arrow.slick-next {
          right: -15px;
        }
      }

      @media (max-width: 600px) {
        .slick-slide {
          padding: 0 8px;
        }

        .slick-arrow {
          width: 32px;
          height: 32px;
        }

        .slick-arrow.slick-prev {
          left: 10px;
        }

        .slick-arrow.slick-next {
          right: 10px;
        }

        .slick-dots {
          bottom: -30px;
        }
      }

      @media (max-width: 480px) {
        .slick-slide {
          padding: 0 6px;
        }

        .slick-dots li {
          margin: 0 2px;
        }

        .slick-dots li button {
          width: 6px;
          height: 6px;
        }
      }
    `;

    const styleElement = document.createElement('style');
    styleElement.innerHTML = sliderStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // Kampanyaları API'den çek (useApiCache ile optimize edildi)
  const { data: campaignData, error: campaignApiError } = useApiCache<Record<string, unknown>[]>(
    'https://360avantajli.com/api/Campaign_Service/campaign',
    undefined,
    5 * 60 * 1000 // 5 dakika cache süresi
  );

  const { data: brandCampaignData } = useApiCache<Record<string, unknown>[]>(
    'https://360avantajli.com/api/Campaign_Service/brand-to-campaign',
    undefined,
    5 * 60 * 1000 // 5 dakika cache süresi
  );

  const { data: brandData } = useApiCache<Record<string, unknown>[]>(
    'https://360avantajli.com/api/Campaign_Service/brand',
    undefined,
    5 * 60 * 1000 // 5 dakika cache süresi
  );

  // Kampanya verilerini işle
  useEffect(() => {
    // API'den veri gelmemişse veya hata varsa işlemi sonlandır
    if (!campaignData || campaignApiError) {
      if (campaignApiError) {
        setError('Failed to load campaigns');
      }
      setCampaignsLoading(false);
      return;
    }

    setCampaignsLoading(true);

    try {
      // Sadece aktif ve süresi geçmemiş kampanyaları filtrele
      const activeCampaigns = campaignData.filter((campaign: Record<string, unknown>) => {
        // Önce aktif olup olmadığını kontrol et
        if (campaign.isActive !== true) {
          return false;
        }

        // Kampanya bitiş tarihini standartlaştır
        const standardizedEndDate = standardizeCampaignEndDate(
          campaign.endDate as string | undefined,
          campaign.updatedAt as string | undefined
        );

        // Süresi geçmiş kampanyaları filtrele
        return !isExpired(standardizedEndDate);
      });

      // API yanıtını Campaign arayüzüne dönüştür
      const mappedCampaigns = activeCampaigns.map((campaign: Record<string, unknown>) => {
        // Kampanyaya ait marka ilişkisini bul
        let brandInfo: {
          name: string;
          logoUrl: string;
          brandUrl: string;
        } = {
          name: 'Marka',
          logoUrl: '',
          brandUrl: ''
        };
        // Default to undefined for brandId
        let brandCampaign: Record<string, unknown> | null = null;

        if (brandCampaignData && Array.isArray(brandCampaignData)) {
          // Bu kampanyaya ait aktif marka ilişkisini bul
          brandCampaign = brandCampaignData.find((bc: Record<string, unknown>) =>
            (bc.campaign as Record<string, unknown>)?.id === campaign.id && bc.isActive === true
          ) || null;

          if (brandCampaign && 'brand' in brandCampaign) {
            // Marka bilgilerini al
            if (brandCampaign && 'brand' in brandCampaign && typeof brandCampaign.brand === 'object') {
              const brand = brandCampaign.brand as Record<string, unknown>;
              brandInfo = {
                name: (brand.name as string) || 'Marka',
                logoUrl: ((brand.logo as string) || (brand.logoUrl as string) || ''),
                brandUrl: (brand.brandUrl as string) || ''
              };
            }
          }
        }

        const id = campaign.id ? (campaign.id as string | number).toString() : '';
        const brandId = brandCampaign && 'brand' in brandCampaign && brandCampaign.brand &&
                        typeof brandCampaign.brand === 'object' && 'id' in (brandCampaign.brand as object) ?
                        ((brandCampaign.brand as Record<string, unknown>).id as string | number).toString() : undefined;
        // Kategori bilgilerini detaylı şekilde al
        let categoryId = '0';
        let parentCategoryId = '0';
        let categoryName = '';

        if (campaign.category && typeof campaign.category === 'object') {
          const category = campaign.category as Record<string, unknown>;
          categoryId = category.id ? (category.id as string | number).toString() : '0';
          parentCategoryId = category.parentCategoryId ? (category.parentCategoryId as string | number).toString() : '0';
          categoryName = (category.name as string) || '';
        }

        // Kampanya detaylarını işle
        let campaignDetails = {};
        if (campaign.details && typeof campaign.details === 'object') {
          campaignDetails = campaign.details;
        }

        return {
          id: id,
          title: (campaign.name as string) || '',
          brand: brandInfo.name,
          description: (campaign.description as string) || '',
          imageUrl: (campaign.imageUrl as string) || '/placeholder-image.jpg',
          logoUrl: brandInfo.logoUrl || '',
          brandUrl: brandInfo.brandUrl || '',
          brandId: brandId,
          endDate: standardizeCampaignEndDate(
            campaign.endDate as string | undefined,
            campaign.updatedAt as string | undefined
          ),
          discount: campaign.discount ? `%${campaign.discount as string}` : '%0',
          category: categoryId,
          parentCategoryId: parentCategoryId,
          categoryName: categoryName,
          createdAt: (campaign.createdAt as string) || undefined,
          features: {
            price: (campaign.price as string) || '0 TL',
            monthlyPayment: (campaign.monthlyPayment as string) || '0 TL',
            term: (campaign.term as string) || '0 ay',
            downPayment: (campaign.downPayment as string) || '0 TL',
            interestRate: (campaign.interestRate as string) || '%0',
          },
          details: campaignDetails as Record<string, CampaignDetailField>
        };
      });

      setCampaigns(mappedCampaigns);
      setCampaignsLoading(false);
    } catch {
      setError('Failed to load campaigns');
      setCampaignsLoading(false);
    }
  }, [campaignData, brandCampaignData, campaignApiError, standardizeCampaignEndDate, isExpired]);

  // Marka verilerini işle
  useEffect(() => {
    if (!brandData) {
      setBrandsLoading(false);
      return;
    }

    setBrandsLoading(true);
    try {
      // Sadece aktif markaları filtrele
      const activeBrands = (brandData as unknown as Brand[]).filter((brand: Brand) =>
        brand.isActive !== false
      );
      setBrands(activeBrands);
      setBrandsLoading(false);
    } catch {
      setError('Failed to load brands');
      setBrandsLoading(false);
    }
  }, [brandData]);

  // Slider ayarlarını dinamikleştir (her zaman slider, 1-2 için 3 slot, centerMode)
  const getSliderSettings = (campaignCount: number) => {
    if (campaignCount < 3) {
      return {
        dots: false,
        infinite: false,
        speed: 500,
        slidesToShow: 3,
        slidesToScroll: 1,
        arrows: false,
        autoplay: false,
        centerMode: false,
        centerPadding: '0px',
        swipeToSlide: false,
        draggable: false,
        responsive: [
          {
            breakpoint: 1024,
            settings: {
              slidesToShow: 1,
              slidesToScroll: 1,
              centerMode: false,
            }
          }
        ]
      };
    }
    return SLIDER_SETTINGS;
  };

  const [popularBrandIds, setPopularBrandIds] = useState<number[]>([]);
  const [popularCampaignIds, setPopularCampaignIds] = useState<string[]>([]);

  useEffect(() => {
    const uniqId = getOrCreateUniqueId();
    // Popüler markalar
    axios.get(`https://360avantajli.com/api/Campaign_Service/events/user/${uniqId}/popular/brands`)
      .then(res => setPopularBrandIds(Array.isArray(res.data) ? res.data.map(item => item.id) : []))
      .catch(() => setPopularBrandIds([]));
    // Popüler kampanyalar
    axios.get(`https://360avantajli.com/api/Campaign_Service/events/user/${uniqId}/popular/campaigns`)
      .then(res => setPopularCampaignIds(Array.isArray(res.data) ? res.data.map(item => item.id.toString()) : []))
      .catch(() => setPopularCampaignIds([]));
  }, []);

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          bgcolor: 'primary.main',
          color: 'primary.contrastText',
          py: {
            xs: 4, // Mobil için daha az padding
            sm: 6,
            md: 8,
          },
          mb: {
            xs: 3, // Mobil için daha az margin
            sm: 4,
            md: 6,
          },
        }}
      >
        <Container maxWidth="lg">
          <Typography
            variant="h2"
            component="h1"
            align="center"
            gutterBottom
            sx={{
              fontWeight: 800,
              fontSize: {
                xs: '1.75rem', // Mobil için daha küçük
                sm: '2.25rem',
                md: '2.5rem',
              },
            }}
          >
            {intl.formatMessage({ id: 'home.hero.title' })}
          </Typography>
          <Typography
            variant="h5"
            align="center"
            sx={{
              maxWidth: {
                xs: '100%', // Mobil için tam genişlik
                sm: '600px',
                md: '800px',
              },
              mx: 'auto',
              color: '#fff',
              textShadow: '0 2px 4px rgba(0,0,0,0.2)',
              backgroundColor: 'rgba(0,0,0,0.1)',
              padding: {
                xs: '12px', // Mobil için daha az padding
                sm: '14px',
                md: '16px',
              },
              borderRadius: {
                xs: '6px',
                sm: '7px',
                md: '8px',
              },
              backdropFilter: 'blur(4px)',
              fontWeight: 500,
              letterSpacing: '0.5px',
              lineHeight: 1.4,
              fontSize: {
                xs: '1rem', // Mobil için daha küçük
                sm: '1.125rem',
                md: '1.25rem',
              },
            }}
          >
            {intl.formatMessage({ id: 'home.hero.subtitle' })}
          </Typography>
        </Container>
      </Box>

      <Container maxWidth="xl">
        {/* Loading State */}
        {(brandsLoading || campaignsLoading) ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
            <CircularProgress size={60} />
          </Box>
        ) : error ? (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" color="error">
              {error}
            </Typography>
          </Box>
        ) : (
          /* Brand Groups */
          brands.map((brand) => {
            // Bu markaya ait kampanyaları filtrele
            const brandCampaigns = campaigns.filter(campaign => {
              try {
                // Kampanyanın marka ID'sini kontrol et
                return campaign.brandId === brand.id.toString();
              } catch {
                return false;
              }
            });

            // Eğer bu marka için kampanya yoksa, markayı gösterme
            if (brandCampaigns.length === 0) {
              return null;
            }

            // Kampanyaları bitiş tarihine göre sırala (en yakın tarihten en uzağa)
            const sortedCampaigns = sortCampaignsByEndDate(brandCampaigns);

            return (
              <Box key={brand.id} sx={{ mb: 6 }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 3,
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      cursor: 'pointer',
                      '&:hover': {
                        opacity: 0.8,
                        transform: 'translateY(-1px)',
                      },
                      transition: 'all 0.2s ease',
                    }}
                    onClick={() => {
                      // Marka sayfasına yönlendir
                      const brandSlug = brand.name ? brand.name.toLowerCase().replace(/\s+/g, '-') : brand.id.toString();

                      if (isTestMode) {
                        navigate(`/test/marka/${brandSlug}`);
                      } else {
                        navigate(`/marka/${brandSlug}`);
                      }
                    }}
                  >
                    <Typography
                      variant="h5"
                      component="h2"
                      sx={{
                        fontWeight: 700,
                        color: theme.palette.mode === 'dark' ? 'primary.light' : 'text.primary',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        fontSize: { xs: '1.5rem', md: '1.75rem' },
                        textShadow: theme.palette.mode === 'dark' ? '0 2px 4px rgba(0,0,0,0.2)' : 'none',
                      }}
                    >
                      <Box
                        component="img"
                        src={`https://360avantajli.com/api/Campaign_Service/brand/${brand.id}/image`}
                        alt={`${brand.name} logo`}
                        sx={{
                          width: 40,
                          height: 40,
                          borderRadius: '50%',
                          objectFit: 'contain',
                          backgroundColor: 'white',
                          padding: '4px',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                          transition: 'transform 0.2s ease',
                          '&:hover': {
                            transform: 'scale(1.05)',
                          },
                        }}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.onerror = null;
                          target.src = '/placeholder-logo.jpg';
                        }}
                      />
                      {brand.name}
                    </Typography>
                  </Box>
                  <Button
                    variant="outlined"
                    onClick={() => {
                      // Marka sayfasına yönlendir
                      const brandSlug = brand.name ? brand.name.toLowerCase().replace(/\s+/g, '-') : brand.id.toString();

                      if (isTestMode) {
                        navigate(`/test/marka/${brandSlug}`);
                      } else {
                        navigate(`/marka/${brandSlug}`);
                      }
                    }}
                    sx={{
                      textTransform: 'none',
                      fontWeight: 600,
                      color: theme.palette.mode === 'dark' ? 'primary.light' : 'primary.main',
                      borderColor: theme.palette.mode === 'dark' ? 'primary.light' : 'primary.main',
                      '&:hover': {
                        borderColor: theme.palette.mode === 'dark' ? 'primary.main' : 'primary.dark',
                        backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                      },
                    }}
                  >
                    {intl.formatMessage({ id: 'common.viewAll' })}
                  </Button>
                </Box>

                {/* Kampanya gösterimi: 1, 2 ve 3+ için aynı UI, sadece 3+ için slider */}
                <Slider {...getSliderSettings(sortedCampaigns.length)}>
                  {sortedCampaigns.map(campaign => (
                    <CustomSlide
                      key={campaign.id}
                      campaign={campaign}
                    />
                  ))}
                </Slider>
              </Box>
            );
          })
        )}

        {/* En Popüler Markalar Section */}
        {!brandsLoading && !campaignsLoading && brands.length > 0 && (
          <Box sx={{ mb: 6, mt: 4 }}>
            <Typography
              variant="h4"
              component="h2"
              align="center"
              sx={{
                fontWeight: 700,
                mb: 4,
                color: theme.palette.mode === 'dark' ? 'primary.light' : 'text.primary',
                fontSize: { xs: '1.75rem', md: '2rem' },
              }}
            >
              {intl.formatMessage({ id: 'home.popularBrands.title' }, { defaultMessage: 'En Popüler Markalar' })}
            </Typography>

            <Box
              sx={{
                display: 'flex',
                overflowX: 'auto',
                gap: 1,
                pb: 1,
                cursor: 'grab',
                userSelect: 'none',
                '&::-webkit-scrollbar': {
                  display: 'none', // Scrollbar'ı gizle
                },
                scrollbarWidth: 'none', // Firefox için scrollbar'ı gizle
                msOverflowStyle: 'none', // IE için scrollbar'ı gizle
              }}
              onMouseDown={handleMouseDown}
              onMouseLeave={handleMouseLeave}
              onMouseUp={handleMouseUp}
              onMouseMove={handleMouseMove}
            >
              {sortBrandsByPopularity(brands, popularBrandIds).slice(0, 24).map((brand) => (
                <Box
                  key={brand.id}
                  sx={{
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    flexShrink: 0,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    '&:hover': {
                      transform: 'translateY(-2px) scale(1.03)',
                      filter: 'brightness(1.05)',
                    },
                  }}
                  onClick={(e) => {
                    // Sürükle işlemi sırasında tıklama olayını engelle
                    if (isDragging) {
                      e.preventDefault();
                      return;
                    }

                    const brandSlug = brand.name ? brand.name.toLowerCase().replace(/\s+/g, '-') : brand.id.toString();

                    if (isTestMode) {
                      navigate(`/test/marka/${brandSlug}`);
                    } else {
                      navigate(`/marka/${brandSlug}`);
                    }
                  }}
                >
                  <Box
                    sx={{
                      width: { xs: 80, sm: 85, md: 90 },
                      height: { xs: 80, sm: 85, md: 90 },
                      borderRadius: '50%',
                      backgroundColor: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: '6px',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                      border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                        borderColor: theme.palette.primary.main,
                      },
                    }}
                  >
                    <Box
                      component="img"
                      src={`https://360avantajli.com/api/Campaign_Service/brand/${brand.id}/image`}
                      alt={`${brand.name} logo`}
                      sx={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'contain',
                        borderRadius: '50%',
                      }}
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.onerror = null;
                        target.src = '/placeholder-logo.jpg';
                      }}
                    />
                  </Box>
                  <Typography
                    variant="caption"
                    align="center"
                    sx={{
                      display: 'block',
                      mt: 0.5,
                      fontWeight: 500,
                      color: theme.palette.mode === 'dark' ? 'text.secondary' : 'text.primary',
                      fontSize: { xs: '0.65rem', sm: '0.7rem' },
                      maxWidth: { xs: 80, sm: 85, md: 90 },
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {brand.name}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        )}

        {/* En Sevilen Kampanyalar Section */}
        {!brandsLoading && !campaignsLoading && campaigns.length > 0 && (
          (() => {
            const activeCampaigns = campaigns.filter(campaign => !isExpired(campaign.endDate));
            const sortedCampaigns = sortCampaignsByPopularity(activeCampaigns, popularCampaignIds);
            return sortedCampaigns.length > 0 ? (
              <Box sx={{ mb: 8, mt: 6 }}>
                <Typography
                  variant="h4"
                  component="h2"
                  align="center"
                  sx={{
                    fontWeight: 700,
                    mb: 4,
                    color: theme.palette.mode === 'dark' ? 'primary.light' : 'text.primary',
                    fontSize: { xs: '1.75rem', md: '2rem' },
                  }}
                >
                  {intl.formatMessage({ id: 'home.favoriteCampaigns.title' }, { defaultMessage: 'En Sevilen Kampanyalar' })}
                </Typography>
                <Box
                  sx={{
                    display: 'grid',
                    gridTemplateColumns: {
                      xs: 'repeat(1, 1fr)',
                      sm: 'repeat(2, 1fr)',
                      md: 'repeat(3, 1fr)',
                      lg: 'repeat(4, 1fr)',
                    },
                    gap: 3,
                  }}
                >
                  {sortedCampaigns.slice(0, 8).map((campaign) => (
                    <Card
                      key={campaign.id}
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        borderRadius: 3,
                        overflow: 'hidden',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        boxShadow: theme.palette.mode === 'dark' ? '0 4px 20px rgba(0, 0, 0, 0.3)' : '0 4px 20px rgba(0, 0, 0, 0.1)',
                        // Bitiş tarihine göre border rengi
                        border: (() => {
                          const daysLeft = getDaysLeft(campaign.endDate);

                          if (daysLeft <= 3 && daysLeft >= 0) return '3px solid #f44336'; // Kırmızı - 3 gün veya daha az
                          return '1px solid rgba(0, 0, 0, 0.12)'; // Normal border
                        })(),
                        '&:hover': {
                          transform: 'translateY(-8px)',
                          boxShadow: theme.palette.mode === 'dark' ? '0 8px 30px rgba(0, 0, 0, 0.4)' : '0 8px 30px rgba(0, 0, 0, 0.15)',
                        },
                      }}
                      onClick={() => {
                        const slug = createSlug(campaign.title);
                        const url = isTestMode ? `/test/campaigns/${slug}` : `/campaigns/${slug}`;
                        navigate(url);
                      }}
                    >
                      <Box sx={{ position: 'relative' }}>
                        <Box
                          component="img"
                          src={`https://360avantajli.com/api/Campaign_Service/campaign-image/showcase/${campaign.id}`}
                          alt={campaign.title}
                          sx={{
                            width: '100%',
                            height: 200,
                            objectFit: 'cover',
                            objectPosition: 'center',
                          }}
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.onerror = null;
                            target.src = campaign.imageUrl || '/placeholder-image.jpg';
                          }}
                        />

                        {/* Marka logosu sol üst köşede */}
                        {campaign.brandId && (
                          <Box
                            sx={{
                              position: 'absolute',
                              top: 12,
                              left: 12,
                              width: 40,
                              height: 40,
                              borderRadius: '50%',
                              backgroundColor: 'white',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              padding: '4px',
                              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
                            }}
                          >
                            <Box
                              component="img"
                              src={`https://360avantajli.com/api/Campaign_Service/brand/${campaign.brandId}/image`}
                              alt={`${campaign.brand} logo`}
                              sx={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'contain',
                                borderRadius: '50%',
                              }}
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.onerror = null;
                                target.src = '/placeholder-logo.jpg';
                              }}
                            />
                          </Box>
                        )}

                        {/* İndirim badge'i sağ üst köşede */}
                        {campaign.discount && campaign.discount !== '%0' && (
                          <Box
                            sx={{
                              position: 'absolute',
                              top: 12,
                              right: 12,
                              backgroundColor: '#4caf50',
                              color: 'white',
                              padding: '4px 8px',
                              borderRadius: 2,
                              fontSize: '0.75rem',
                              fontWeight: 600,
                              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
                            }}
                          >
                            {campaign.discount}
                          </Box>
                        )}
                      </Box>

                      <Box sx={{ p: 2, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                        <Typography
                          variant="h6"
                          sx={{
                            fontWeight: 600,
                            mb: 1,
                            fontSize: '1rem',
                            lineHeight: 1.3,
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            minHeight: '2.6em',
                          }}
                        >
                          {campaign.title}
                        </Typography>

                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            mb: 1.5,
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            minHeight: '2.4em',
                            fontSize: '0.875rem',
                          }}
                        >
                          {campaign.description}
                        </Typography>

                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'flex-end',
                            mt: 'auto',
                            pt: 1,
                          }}
                        >
                          <Box>
                            <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{
                                fontSize: '0.75rem',
                                display: 'block',
                                mb: 0.5,
                              }}
                            >
                              {campaign.brand}
                            </Typography>

                            {/* Fiyat bilgisi */}
                            {campaign.features?.price && campaign.features.price !== '0 TL' && (
                              <Typography
                                variant="caption"
                                color="success.main"
                                sx={{
                                  fontSize: '0.8rem',
                                  fontWeight: 600,
                                  display: 'block',
                                }}
                              >
                                {campaign.features.price}
                              </Typography>
                            )}
                          </Box>

                          <Box sx={{ textAlign: 'right' }}>
                            {/* Bitiş tarihi */}
                            <Typography
                              variant="caption"
                              color={
                                (() => {
                                  const daysLeft = getDaysLeft(campaign.endDate);

                                  if (daysLeft <= 1) return 'error.main';
                                  if (daysLeft <= 3) return 'warning.main';
                                  return 'text.secondary';
                                })()
                              }
                              sx={{
                                fontSize: '0.7rem',
                                display: 'block',
                                mb: 0.5,
                              }}
                            >
                              {(() => {
                                const daysLeft = getDaysLeft(campaign.endDate);

                                if (daysLeft < 0) return intl.formatMessage({ id: 'campaign.badge.expired' });
                                if (daysLeft === 0) return intl.formatMessage({ id: 'campaign.badge.endingToday' });
                                if (daysLeft === 1) return intl.formatMessage({ id: 'campaign.oneDayLeft' });
                                if (daysLeft <= 7) return intl.formatMessage({ id: 'campaign.badge.daysLeft' }, { days: daysLeft });

                                const endDate = new Date(campaign.endDate);
                                return endDate.toLocaleDateString(intl.locale === 'tr-TR' ? 'tr-TR' : 'en-US', {
                                  day: '2-digit',
                                  month: '2-digit',
                                  year: 'numeric'
                                });
                              })()}
                            </Typography>

                            {/* Aylık ödeme bilgisi */}
                            {campaign.features?.monthlyPayment && campaign.features.monthlyPayment !== '0 TL' && (
                              <Typography
                                variant="caption"
                                color="primary.main"
                                sx={{
                                  fontSize: '0.75rem',
                                  fontWeight: 500,
                                  display: 'block',
                                }}
                              >
                                {campaign.features.monthlyPayment}/ay
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </Box>
                    </Card>
                  ))}
                </Box>
              </Box>
            ) : null;
          })()
        )}

        {/* En Yeni Eklenen Kampanyalar Section */}
        {!brandsLoading && !campaignsLoading && campaigns.length > 0 && (() => {
          const newestCampaigns = campaigns.filter(campaign =>
            campaign.createdAt && isNewlyAdded(campaign.createdAt) && !isExpired(campaign.endDate)
          ).slice(0, 8);

          return newestCampaigns.length > 0 ? (
            <Box sx={{ mb: 8, mt: 6 }}>
              <Typography
                variant="h4"
                component="h2"
                align="center"
                sx={{
                  fontWeight: 700,
                  mb: 4,
                  color: theme.palette.mode === 'dark' ? 'primary.light' : 'text.primary',
                  fontSize: { xs: '1.75rem', md: '2rem' },
                }}
              >
                {intl.formatMessage({ id: 'home.newestCampaigns.title' }, { defaultMessage: 'En Yeni Eklenen Kampanyalar' })}
              </Typography>

              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: {
                    xs: 'repeat(1, 1fr)',
                    sm: 'repeat(2, 1fr)',
                    md: 'repeat(3, 1fr)',
                    lg: 'repeat(4, 1fr)',
                  },
                  gap: 3,
                }}
              >
                {newestCampaigns.map((campaign) => (
                  <Card
                    key={campaign.id}
                    sx={{
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      borderRadius: 3,
                      overflow: 'hidden',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      boxShadow: theme.palette.mode === 'dark' ? '0 4px 20px rgba(0, 0, 0, 0.3)' : '0 4px 20px rgba(0, 0, 0, 0.1)',
                      // Bitiş tarihine göre border rengi
                      border: (() => {
                        const daysLeft = getDaysLeft(campaign.endDate);

                        if (daysLeft <= 3 && daysLeft >= 0) return '3px solid #f44336'; // Kırmızı - 3 gün veya daha az
                        return '1px solid rgba(0, 0, 0, 0.12)'; // Normal border
                      })(),
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: theme.palette.mode === 'dark' ? '0 8px 30px rgba(0, 0, 0, 0.4)' : '0 8px 30px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                    onClick={() => {
                      const slug = createSlug(campaign.title);
                      const url = isTestMode ? `/test/campaigns/${slug}` : `/campaigns/${slug}`;
                      navigate(url);
                    }}
                  >
                    <Box sx={{ position: 'relative' }}>
                      <Box
                        component="img"
                        src={`https://360avantajli.com/api/Campaign_Service/campaign-image/showcase/${campaign.id}`}
                        alt={campaign.title}
                        sx={{
                          width: '100%',
                          height: 200,
                          objectFit: 'cover',
                          objectPosition: 'center',
                        }}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.onerror = null;
                          target.src = campaign.imageUrl || '/placeholder-image.jpg';
                        }}
                      />

                      {/* Marka logosu sol üst köşede */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 12,
                          left: 12,
                          width: 40,
                          height: 40,
                          borderRadius: '50%',
                          backgroundColor: 'white',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          padding: '4px',
                          boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                          cursor: 'pointer',
                          transition: 'transform 0.2s ease',
                          '&:hover': {
                            transform: 'scale(1.1)',
                          },
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (campaign.brandId) {
                            const brandSlug = campaign.brand ? campaign.brand.toLowerCase().replace(/\s+/g, '-') : campaign.brandId;
                            if (isTestMode) {
                              navigate(`/test/marka/${brandSlug}`);
                            } else {
                              navigate(`/marka/${brandSlug}`);
                            }
                          }
                        }}
                      >
                        <Box
                          component="img"
                          src={`https://360avantajli.com/api/Campaign_Service/brand/${campaign.brandId}/image`}
                          alt={`${campaign.brand} logo`}
                          sx={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'contain',
                            borderRadius: '50%',
                          }}
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.onerror = null;
                            target.src = campaign.logoUrl || '/placeholder-logo.jpg';
                          }}
                        />
                      </Box>

                      {/* Yeni kampanya badge'i */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 12,
                          right: 12,
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'flex-end',
                          gap: 0.5,
                        }}
                      >
                        <Box
                          sx={{
                            backgroundColor: '#4caf50',
                            color: 'white',
                            px: 1.5,
                            py: 0.5,
                            borderRadius: 2,
                            fontSize: '0.75rem',
                            fontWeight: 600,
                            boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                          }}
                        >
                          {intl.formatMessage({ id: 'campaign.badge.new' })}
                        </Box>
                        {campaign.createdAt && (
                          <Box
                            sx={{
                              backgroundColor: 'rgba(76, 175, 80, 0.9)',
                              color: 'white',
                              px: 1,
                              py: 0.25,
                              borderRadius: 1,
                              fontSize: '0.65rem',
                              fontWeight: 500,
                              boxShadow: '0 1px 4px rgba(0,0,0,0.2)',
                              whiteSpace: 'nowrap',
                            }}
                          >
                            {getTimeAgo(campaign.createdAt)}
                          </Box>
                        )}
                      </Box>
                    </Box>

                    <Box sx={{ p: 2, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                      <Typography
                        variant="h6"
                        component="h3"
                        sx={{
                          fontWeight: 600,
                          mb: 1,
                          fontSize: '1rem',
                          lineHeight: 1.3,
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          minHeight: '2.6em',
                        }}
                      >
                        {campaign.title}
                      </Typography>

                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          mb: 2,
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          flexGrow: 1,
                          fontSize: '0.875rem',
                          lineHeight: 1.4,
                        }}
                      >
                        {campaign.description}
                      </Typography>

                      <Box sx={{ mt: 'auto' }}>
                        {/* Fiyat bilgisi */}
                        {campaign.features?.price && campaign.features.price !== '0 TL' && (
                          <Typography
                            variant="h6"
                            color="primary.main"
                            sx={{
                              fontWeight: 700,
                              fontSize: '1.1rem',
                              mb: 0.5,
                            }}
                          >
                            {campaign.features.price}
                          </Typography>
                        )}

                        {/* Aylık ödeme bilgisi */}
                        {campaign.features?.monthlyPayment && campaign.features.monthlyPayment !== '0 TL' && (
                          <Typography
                            variant="caption"
                            color="primary.main"
                            sx={{
                              fontSize: '0.75rem',
                              fontWeight: 500,
                              display: 'block',
                            }}
                          >
                            {campaign.features.monthlyPayment}/ay
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Card>
                ))}
              </Box>
            </Box>
          ) : null;
        })()}

        {/* Bitiş Tarihi Yaklaşan Kampanyalar Section */}
        {!brandsLoading && !campaignsLoading && campaigns.length > 0 && (() => {
          const endingSoonCampaigns = campaigns
            .filter(campaign => isEndingSoonInWeek(campaign.endDate))
            .sort((a, b) => {
              // En yakın bitecekten en geç biteceğe doğru sırala
              const daysLeftA = getDaysLeft(a.endDate);
              const daysLeftB = getDaysLeft(b.endDate);
              return daysLeftA - daysLeftB;
            })
            .slice(0, 8);

          return endingSoonCampaigns.length > 0 ? (
            <Box sx={{ mb: 8, mt: 6 }}>
              <Typography
                variant="h4"
                component="h2"
                align="center"
                sx={{
                  fontWeight: 700,
                  mb: 4,
                  color: theme.palette.mode === 'dark' ? 'primary.light' : 'text.primary',
                  fontSize: { xs: '1.75rem', md: '2rem' },
                }}
              >
                {intl.formatMessage({ id: 'home.endingSoonCampaigns.title' }, { defaultMessage: 'Bitiş Tarihi Yaklaşan Kampanyalar' })}
              </Typography>

              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: {
                    xs: 'repeat(1, 1fr)',
                    sm: 'repeat(2, 1fr)',
                    md: 'repeat(3, 1fr)',
                    lg: 'repeat(4, 1fr)',
                  },
                  gap: 3,
                }}
              >
                {endingSoonCampaigns.map((campaign) => (
                  <Card
                    key={campaign.id}
                    sx={{
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      borderRadius: 3,
                      overflow: 'hidden',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      boxShadow: theme.palette.mode === 'dark' ? '0 4px 20px rgba(0, 0, 0, 0.3)' : '0 4px 20px rgba(0, 0, 0, 0.1)',
                      // Bitiş tarihine göre border rengi - hepsi yaklaşan olduğu için kırmızı
                      border: '3px solid #f44336',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: theme.palette.mode === 'dark' ? '0 8px 30px rgba(0, 0, 0, 0.4)' : '0 8px 30px rgba(0, 0, 0, 0.15)',
                      },
                    }}
                    onClick={() => {
                      const slug = createSlug(campaign.title);
                      const url = isTestMode ? `/test/campaigns/${slug}` : `/campaigns/${slug}`;
                      navigate(url);
                    }}
                  >
                    <Box sx={{ position: 'relative' }}>
                      <Box
                        component="img"
                        src={`https://360avantajli.com/api/Campaign_Service/campaign-image/showcase/${campaign.id}`}
                        alt={campaign.title}
                        sx={{
                          width: '100%',
                          height: 200,
                          objectFit: 'cover',
                          objectPosition: 'center',
                        }}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.onerror = null;
                          target.src = campaign.imageUrl || '/placeholder-image.jpg';
                        }}
                      />

                      {/* Marka logosu sol üst köşede */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 12,
                          left: 12,
                          width: 40,
                          height: 40,
                          borderRadius: '50%',
                          backgroundColor: 'white',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          padding: '4px',
                          boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                          cursor: 'pointer',
                          transition: 'transform 0.2s ease',
                          '&:hover': {
                            transform: 'scale(1.1)',
                          },
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (campaign.brandId) {
                            const brandSlug = campaign.brand ? campaign.brand.toLowerCase().replace(/\s+/g, '-') : campaign.brandId;
                            if (isTestMode) {
                              navigate(`/test/marka/${brandSlug}`);
                            } else {
                              navigate(`/marka/${brandSlug}`);
                            }
                          }
                        }}
                      >
                        <Box
                          component="img"
                          src={`https://360avantajli.com/api/Campaign_Service/brand/${campaign.brandId}/image`}
                          alt={`${campaign.brand} logo`}
                          sx={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'contain',
                            borderRadius: '50%',
                          }}
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.onerror = null;
                            target.src = campaign.logoUrl || '/placeholder-logo.jpg';
                          }}
                        />
                      </Box>

                      {/* Son fırsat badge'i */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 12,
                          right: 12,
                          backgroundColor: '#f44336',
                          color: 'white',
                          px: 1.5,
                          py: 0.5,
                          borderRadius: 2,
                          fontSize: '0.75rem',
                          fontWeight: 600,
                          boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
                          animation: 'pulse 2s infinite',
                          '@keyframes pulse': {
                            '0%': { opacity: 1 },
                            '50%': { opacity: 0.7 },
                            '100%': { opacity: 1 }
                          }
                        }}
                      >
                        {intl.formatMessage({ id: 'campaign.badge.lastChance' })}
                      </Box>

                      {/* Kalan süre */}
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 12,
                          right: 12,
                          backgroundColor: 'rgba(244, 67, 54, 0.9)',
                          color: 'white',
                          px: 1,
                          py: 0.5,
                          borderRadius: 1,
                          fontSize: '0.7rem',
                          fontWeight: 600,
                        }}
                      >
                        {getTimeRemaining(campaign.endDate)}
                      </Box>
                    </Box>

                    <Box sx={{ p: 2, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                      <Typography
                        variant="h6"
                        component="h3"
                        sx={{
                          fontWeight: 600,
                          mb: 1,
                          fontSize: '1rem',
                          lineHeight: 1.3,
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          minHeight: '2.6em',
                        }}
                      >
                        {campaign.title}
                      </Typography>

                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          mb: 2,
                          display: '-webkit-box',
                          WebkitLineClamp: 3,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          flexGrow: 1,
                          fontSize: '0.875rem',
                          lineHeight: 1.4,
                        }}
                      >
                        {campaign.description}
                      </Typography>

                      <Box sx={{ mt: 'auto' }}>
                        {/* Fiyat bilgisi */}
                        {campaign.features?.price && campaign.features.price !== '0 TL' && (
                          <Typography
                            variant="h6"
                            color="primary.main"
                            sx={{
                              fontWeight: 700,
                              fontSize: '1.1rem',
                              mb: 0.5,
                            }}
                          >
                            {campaign.features.price}
                          </Typography>
                        )}

                        {/* Aylık ödeme bilgisi */}
                        {campaign.features?.monthlyPayment && campaign.features.monthlyPayment !== '0 TL' && (
                          <Typography
                            variant="caption"
                            color="primary.main"
                            sx={{
                              fontSize: '0.75rem',
                              fontWeight: 500,
                              display: 'block',
                            }}
                          >
                            {campaign.features.monthlyPayment}/ay
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </Card>
                ))}
              </Box>
            </Box>
          ) : null;
        })()}

        {/* Stats Section */}
        <StatsSection />

        {/* Newsletter Section */}
        <NewsletterSection />
      </Container>
    </Box>
  );
};

export default Home;