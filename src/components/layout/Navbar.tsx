import React, { useState } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { createSlug } from '../../utils/urlUtils';
import { useTestMode } from '../common/TestModeWrapper';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Container,
  InputBase,
  alpha,
  styled,
  Box,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Popover,
  IconButton,
  Stack,
  useTheme,
  Drawer,
  List,
  ListItem,
  CircularProgress,
  Divider,
  Menu,
  Avatar,
  Tooltip,
} from '@mui/material';
import {
  Search as SearchIcon,
  RequestQuote as RequestQuoteIcon,
  Menu as MenuIcon,
  KeyboardArrowDown as ArrowDownIcon,
  ChevronRight as ChevronRightIcon,
  ExpandMore,
  ExpandLess,
  Login as LoginIcon,
  Logout as LogoutIcon,
  Person as PersonIcon,
  AdminPanelSettings as AdminIcon,
} from '@mui/icons-material';
import LanguageToggle from '../common/LanguageToggle';
import { useIntl } from 'react-intl';
import { useCategories, CategoryWithUI } from '../../contexts/CategoryContext';
import QuoteForm from '../features/quote/QuoteForm';
import { useAuth } from '../../contexts/AuthContext';


const Search = styled('div')(({ theme }) => ({
  position: 'relative',
  borderRadius: theme.shape.borderRadius * 3,
  backgroundColor: alpha(theme.palette.common.black, 0.04),
  '&:hover': {
    backgroundColor: alpha(theme.palette.common.black, 0.07),
  },
  marginLeft: theme.spacing(2),
  width: '300px',
  transition: theme.transitions.create(['background-color', 'box-shadow']),
  [theme.breakpoints.down('sm')]: {
    width: '100%',
    marginLeft: 0,
  },
}));

const SearchIconWrapper = styled('div')(({ theme }) => ({
  padding: theme.spacing(0, 2),
  height: '100%',
  position: 'absolute',
  pointerEvents: 'none',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: alpha(theme.palette.common.black, 0.4),
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: theme.palette.text.primary,
  width: '100%',
  '& .MuiInputBase-input': {
    padding: '10px 10px 10px 0',
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    width: '100%',
    height: '24px',
    fontSize: '14px',
    transition: theme.transitions.create('width'),
    '&::placeholder': {
      color: alpha(theme.palette.grey[500], 0.7),
      opacity: 1,
    },
    '&:focus': {
      '& + fieldset': {
        borderColor: theme.palette.primary.main,
      },
    },
  },
}));

const CategoryPopover = styled(Popover)(({ theme }) => ({
  '& .MuiPaper-root': {
    width: '400px',
    borderRadius: '12px',
    boxShadow: '0 8px 24px rgba(149, 157, 165, 0.2)',
    marginTop: '8px',
    border: `1px solid ${alpha(theme.palette.common.black, 0.05)}`,
  },
}));


const CategoryList = styled(Box)(({ theme }) => ({
  padding: '12px',
  maxHeight: '500px',
  overflowY: 'auto',
  '&::-webkit-scrollbar': {
    width: '8px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'transparent',
  },
  '&::-webkit-scrollbar-thumb': {
    background: alpha(theme.palette.common.black, 0.1),
    borderRadius: '4px',
  },
}));

const Navbar: React.FC = () => {
  const standardNavigate = useNavigate();
  const { navigate: testNavigate, isTestMode } = useTestMode();
  const navigate = isTestMode ? testNavigate : standardNavigate;

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [userMenuAnchorEl, setUserMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [quoteFormOpen, setQuoteFormOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mobileSearchOpen, setMobileSearchOpen] = useState(false);
  const intl = useIntl();
  const theme = useTheme();
  const { user, isAuthenticated, logout } = useAuth();

  // Log user for debugging
  // Get categories from context
  const { categoryTree, loading, error } = useCategories();

  // State for expanded categories
  const [expandedCategories, setExpandedCategories] = useState<Record<number, boolean>>({});



  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery('');
    }
  };

  const handleQuoteFormOpen = () => {
    setQuoteFormOpen(true);
  };

  const handleQuoteFormClose = () => {
    setQuoteFormOpen(false);
  };

  const handleOpenNavMenu = () => {
    setMobileMenuOpen(true);
  };

  const handleCloseNavMenu = () => {
    setMobileMenuOpen(false);
  };

  const handleCategoryClick = (category: CategoryWithUI) => {
    navigate(`/categories/${createSlug(category.name)}`);
    handleClose();
    if (mobileMenuOpen) {
      handleCloseNavMenu();
    }
  };

  // Toggle category expansion
  const toggleCategoryExpand = (categoryId: number, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // Render a category item for mobile menu
  const renderCategoryItem = (category: CategoryWithUI, onClose: () => void, level = 0) => {
    const hasChildren = category.children && category.children.length > 0;
    const isExpanded = expandedCategories[category.id] || false;

    return (
      <React.Fragment key={category.id}>
        <ListItem
          component="div"
          onClick={() => {
            // Always navigate to category page when clicking on the category name
            handleCategoryClick(category);
            onClose();
          }}
          sx={{
            py: 1,
            px: 2,
            pl: 2 + level * 1.5, // Indent based on level
            borderRadius: 1,
            mb: 0.5,
            cursor: 'pointer',
            '&:hover': {
              bgcolor: alpha('#000', 0.04),
            },
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ListItemIcon sx={{ minWidth: 36, color: 'inherit' }}>
              {category.icon}
            </ListItemIcon>
            <ListItemText
              primary={category.name}
              primaryTypographyProps={{
                variant: 'body2',
                fontWeight: 500,
                sx: {
                  color: 'grey.800',
                },
              }}
            />
          </Box>
          {hasChildren && (
            <IconButton
              size="small"
              onClick={(e) => toggleCategoryExpand(category.id, e)}
              edge="end"
            >
              {isExpanded ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
            </IconButton>
          )}
        </ListItem>

        {/* Render children */}
        {hasChildren && isExpanded && (
          <Box sx={{ ml: 2 }}>
            {category.children.map((child: CategoryWithUI) => renderCategoryItem(child, onClose, level + 1))}
          </Box>
        )}
      </React.Fragment>
    );
  };

  // Render a category menu item for desktop menu
  const renderCategoryMenuItem = (category: CategoryWithUI, level = 0) => {
    const hasChildren = category.children && category.children.length > 0;
    const isExpanded = expandedCategories[category.id] || false;

    return (
      <React.Fragment key={category.id}>
        <MenuItem
          onClick={() => {
            // Always navigate to category page when clicking on the category name
            handleCategoryClick(category);
          }}
          sx={{
            py: 1.5,
            px: 2,
            pl: 2 + level * 1.5, // Indent based on level
            '&:hover': {
              bgcolor: alpha('#000', 0.04),
            },
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {level > 0 && (
              <ChevronRightIcon
                fontSize="small"
                sx={{
                  ml: -1,
                  mr: 0.5,
                  color: 'grey.500',
                  fontSize: '1rem',
                }}
              />
            )}
            <ListItemIcon sx={{ minWidth: 36, color: 'inherit' }}>
              {category.icon}
            </ListItemIcon>
            <ListItemText
              primary={category.name}
              primaryTypographyProps={{
                variant: 'body1',
                fontWeight: level === 0 ? 600 : 500,
                sx: {
                  color: level === 0 ? 'primary.main' : 'grey.800',
                },
              }}
            />
          </Box>
          {hasChildren && (
            <IconButton
              size="small"
              onClick={(e) => toggleCategoryExpand(category.id, e)}
              edge="end"
            >
              {isExpanded ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
            </IconButton>
          )}
        </MenuItem>

        {/* Render children */}
        {hasChildren && isExpanded && (
          <Box sx={{ ml: 2 }}>
            {category.children.map((child: CategoryWithUI) => renderCategoryMenuItem(child, level + 1))}
          </Box>
        )}
      </React.Fragment>
    );
  };



  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch(e as React.FormEvent);
    }
  };

  const handleMobileSearchOpen = () => {
    setMobileSearchOpen(true);
  };

  const handleMobileSearchClose = () => {
    setMobileSearchOpen(false);
    setSearchQuery('');
  };

  // User menu handlers
  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchorEl(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchorEl(null);
  };

  const handleLogin = () => {
    navigate('/login');
    handleUserMenuClose();
  };

  const handleLogout = async () => {
    await logout();
    handleUserMenuClose();
    navigate('/login');
  };

  const handleAdminPanel = () => {
    // Kullanıcı admin değilse, /test sayfasına yönlendir
    if (user?.role !== 'ADMIN') {
      standardNavigate('/test');
      handleUserMenuClose();
      return;
    }

    // Admin ise dashboard'a yönlendir
    standardNavigate('/admin/dashboard');
    handleUserMenuClose();
  };

  const handleProfile = () => {
    navigate('/profile');
    handleUserMenuClose();
  };

  return (
    <AppBar
      position="sticky"
      elevation={0}
      sx={{
        bgcolor: 'white',
        borderBottom: 1,
        borderColor: alpha('#000', 0.1),
      }}
    >
      <Container maxWidth="lg">
        <Toolbar disableGutters sx={{ minHeight: '60px', py: 0.5 }}>
          {/* Mobile Menu Icon */}
          <Box sx={{ display: { xs: 'flex', md: 'none' }, mr: 1 }}>
            <IconButton
              size="small"
              onClick={handleOpenNavMenu}
              sx={{ color: 'grey.700' }}
            >
              <MenuIcon fontSize="medium" />
            </IconButton>
          </Box>

          {/* Mobile Search Icon */}
          <Box sx={{ display: { xs: 'flex', md: 'none' }, mr: 1 }}>
            <IconButton
              size="small"
              onClick={handleMobileSearchOpen}
              sx={{ color: 'grey.700' }}
            >
              <SearchIcon fontSize="medium" />
            </IconButton>
          </Box>

          {/* Logo */}
          <RouterLink to="/test" style={{ textDecoration: 'none', display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="h6"
              noWrap
              sx={{
                mr: 2,
                fontWeight: 700,
                color: theme.palette.primary.main,
                textDecoration: 'none',
                letterSpacing: '-0.5px',
                fontSize: { xs: '1.1rem', md: '1.3rem' },
                transition: 'color 0.2s ease',
                '&:hover': {
                  color: theme.palette.primary.main,
                },
              }}
            >
              360Avantajlı
            </Typography>
          </RouterLink>

          {/* Desktop Navigation */}
          <Box sx={{ display: { xs: 'none', md: 'flex' }, alignItems: 'center' }}>
            {/* Categories Dropdown */}
            <Button
              onClick={handleClick}
              sx={{
                py: 0.75,
                px: 1.5,
                mr: 1,
                color: 'grey.700',
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                borderRadius: 1.5,
                textTransform: 'none',
                fontSize: '0.9rem',
                fontWeight: 500,
                '&:hover': {
                  bgcolor: alpha('#000', 0.04),
                },
              }}
              endIcon={<ArrowDownIcon />}
            >
              {intl.formatMessage({ id: 'navbar.categories' })}
            </Button>

            {/* Brands Link */}
            <Button
              onClick={() => navigate('/test/brands')}
              sx={{
                py: 0.75,
                px: 1.5,
                color: 'grey.700',
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                borderRadius: 1.5,
                textTransform: 'none',
                fontSize: '0.9rem',
                fontWeight: 500,
                '&:hover': {
                  bgcolor: alpha('#000', 0.04),
                },
              }}
            >
              {intl.formatMessage({ id: 'navbar.brands' })}
            </Button>
          </Box>

          {/* Search Bar - Desktop */}
          <Box sx={{ flexGrow: 1, display: { xs: 'none', md: 'flex' }, justifyContent: 'center', px: 2 }}>
            <form onSubmit={handleSearch} style={{ width: '100%', maxWidth: '500px' }}>
              <Search>
                <SearchIconWrapper>
                  <SearchIcon />
                </SearchIconWrapper>
                <StyledInputBase
                  placeholder={intl.formatMessage({ id: 'navbar.search' })}
                  inputProps={{ 'aria-label': 'search' }}
                  value={searchQuery}
                  onChange={handleSearchChange}
                  onKeyPress={handleSearchKeyPress}
                  sx={{
                    color: 'grey.700',
                    '&::placeholder': {
                      color: 'grey.400',
                    },
                  }}
                />
              </Search>
            </form>
          </Box>

          {/* Right Side Actions */}
          <Stack direction="row" spacing={1} alignItems="center">
            {/* Get Quote Button - Desktop */}
            <Button
              variant="contained"
              color="primary"
              startIcon={<RequestQuoteIcon />}
              onClick={handleQuoteFormOpen}
              sx={{
                display: { xs: 'none', sm: 'flex' },
                py: 0.75,
                px: 2,
                height: '36px',
                borderRadius: '8px',
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '0.85rem',
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
                background: theme.palette.primary.main,
                color: '#fff',
                border: 'none',
                '&:hover': {
                  boxShadow: '0 6px 12px rgba(0, 0, 0, 0.15)',
                  background: theme.palette.primary.dark,
                },
              }}
            >
              {intl.formatMessage({ id: 'navbar.getQuote' })}
            </Button>

            {/* Login Button or User Menu */}
            {isAuthenticated ? (
              <>
                <Tooltip title={user?.username || ''}>
                  <IconButton
                    onClick={handleUserMenuOpen}
                    sx={{
                      p: 0.5,
                      border: `1px solid ${theme.palette.primary.main}`,
                      borderRadius: '50%',
                    }}
                  >
                    <Avatar
                      sx={{
                        width: 28,
                        height: 28,
                        bgcolor: theme.palette.primary.main,
                        color: 'white',
                        fontSize: '0.8rem',
                        fontWeight: 'bold',
                      }}
                    >
                      {user?.username?.charAt(0).toUpperCase() || 'U'}
                    </Avatar>
                  </IconButton>
                </Tooltip>
                <Menu
                  anchorEl={userMenuAnchorEl}
                  id="user-menu"
                  open={Boolean(userMenuAnchorEl)}
                  onClose={handleUserMenuClose}
                  onClick={handleUserMenuClose}
                  transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                  anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                  PaperProps={{
                    elevation: 3,
                    sx: {
                      overflow: 'visible',
                      filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',
                      mt: 1.5,
                      borderRadius: 2,
                      minWidth: 180,
                      '&:before': {
                        content: '""',
                        display: 'block',
                        position: 'absolute',
                        top: 0,
                        right: 14,
                        width: 10,
                        height: 10,
                        bgcolor: 'background.paper',
                        transform: 'translateY(-50%) rotate(45deg)',
                        zIndex: 0,
                      },
                    },
                  }}
                >
                  {user?.role === 'ADMIN' && (
                    <MenuItem onClick={handleAdminPanel}>
                      <ListItemIcon>
                        <AdminIcon fontSize="small" />
                      </ListItemIcon>
                      {intl.formatMessage({ id: 'navbar.admin' })}
                    </MenuItem>
                  )}
                  <MenuItem onClick={handleProfile}>
                    <ListItemIcon>
                      <PersonIcon fontSize="small" />
                    </ListItemIcon>
                    {intl.formatMessage({ id: 'navbar.profile' })}
                  </MenuItem>
                  <MenuItem onClick={() => { navigate('/my-campaigns'); handleUserMenuClose(); }}>
                    <ListItemIcon>
                      <RequestQuoteIcon fontSize="small" />
                    </ListItemIcon>
                    İlgilendiğim Kampanyalar
                  </MenuItem>
                  <Divider />
                  <MenuItem onClick={handleLogout}>
                    <ListItemIcon>
                      <LogoutIcon fontSize="small" />
                    </ListItemIcon>
                    {intl.formatMessage({ id: 'navbar.logout' })}
                  </MenuItem>
                </Menu>
              </>
            ) : (
              <Button
                variant="outlined"
                color="primary"
                startIcon={<LoginIcon fontSize="small" />}
                onClick={handleLogin}
                sx={{
                  display: { xs: 'none', sm: 'flex' },
                  py: 0.75,
                  px: 2,
                  height: '36px',
                  borderRadius: '8px',
                  textTransform: 'none',
                  fontWeight: 600,
                  fontSize: '0.85rem',
                  border: `1px solid ${theme.palette.primary.main}`,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.04),
                  },
                }}
              >
                {intl.formatMessage({ id: 'navbar.login' })}
              </Button>
            )}

            {/* Language Toggle */}
            <Box sx={{ ml: 0.5 }}>
              <LanguageToggle />
            </Box>
          </Stack>
        </Toolbar>
      </Container>

      {/* Mobile Search Overlay */}
      <Drawer
        anchor="top"
        open={mobileSearchOpen}
        onClose={handleMobileSearchClose}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            height: '100%',
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            backdropFilter: 'blur(4px)',
          },
        }}
      >
        <Box
          onClick={handleMobileSearchClose}
          sx={{
            height: '100%',
            width: '100%',
            position: 'absolute',
            top: 0,
            left: 0,
          }}
        />
        <Box
          onClick={(e) => e.stopPropagation()}
          sx={{
            p: 2,
            mt: 8,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            position: 'relative',
            zIndex: 1,
          }}
        >
          <Box
            sx={{
              width: '100%',
              maxWidth: '600px',
              bgcolor: 'background.paper',
              borderRadius: 2,
              p: 2,
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
            }}
          >
            <form onSubmit={handleSearch} style={{ width: '100%' }}>
              <Search>
                <SearchIconWrapper>
                  <SearchIcon />
                </SearchIconWrapper>
                <StyledInputBase
                  placeholder={intl.formatMessage({ id: 'navbar.search' })}
                  inputProps={{ 'aria-label': 'search' }}
                  value={searchQuery}
                  onChange={handleSearchChange}
                  onKeyPress={handleSearchKeyPress}
                  autoFocus
                  sx={{
                    color: 'grey.700',
                    '&::placeholder': {
                      color: 'grey.400',
                    },
                  }}
                />
              </Search>
            </form>
          </Box>
        </Box>
      </Drawer>

      {/* Mobile Menu Drawer */}
      <Drawer
        anchor="left"
        open={mobileMenuOpen}
        onClose={handleCloseNavMenu}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            width: '280px',
            boxSizing: 'border-box',
            bgcolor: 'background.paper',
          },
        }}
      >
        <Box sx={{ py: 2, px: 2 }}>
          {/* Mobile Get Quote Button */}
          <Button
            variant="contained"
            color="primary"
            fullWidth
            startIcon={<RequestQuoteIcon />}
            onClick={() => {
              handleQuoteFormOpen();
              handleCloseNavMenu();
            }}
            sx={{
              py: 1.2,
              px: 3,
              mb: 2,
              borderRadius: '12px',
              textTransform: 'none',
              fontWeight: 600,
              fontSize: '0.95rem',
              boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1), inset 0 -2px 4px rgba(0, 0, 0, 0.05)',
              background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
              backdropFilter: 'blur(8px)',
              color: '#fff',
              border: 'none',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              '&:hover': {
                boxShadow: '0 12px 24px rgba(0, 0, 0, 0.15), inset 0 -2px 6px rgba(0, 0, 0, 0.08)',
                transform: 'translateY(-2px)',
                background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
                border: 'none',
              },
              '&:active': {
                transform: 'translateY(0)',
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1), inset 0 -1px 2px rgba(0, 0, 0, 0.05)',
              },
            }}
          >
            {intl.formatMessage({ id: 'navbar.getQuote' })}
          </Button>

          {/* Mobile Login/Logout Button */}
          {isAuthenticated ? (
            <>
              {user?.role === 'ADMIN' && (
                <Button
                  variant="outlined"
                  color="primary"
                  fullWidth
                  startIcon={<AdminIcon />}
                  onClick={() => {
                    handleAdminPanel();
                    handleCloseNavMenu();
                  }}
                  sx={{
                    py: 1.2,
                    px: 3,
                    mb: 2,
                    borderRadius: '12px',
                    textTransform: 'none',
                    fontWeight: 600,
                    fontSize: '0.95rem',
                  }}
                >
                  {intl.formatMessage({ id: 'navbar.admin' })}
                </Button>
              )}
              <Button
                variant="outlined"
                color="primary"
                fullWidth
                startIcon={<PersonIcon />}
                onClick={() => {
                  handleProfile();
                  handleCloseNavMenu();
                }}
                sx={{
                  py: 1.2,
                  px: 3,
                  mb: 2,
                  borderRadius: '12px',
                  textTransform: 'none',
                  fontWeight: 600,
                  fontSize: '0.95rem',
                }}
              >
                {intl.formatMessage({ id: 'navbar.profile' })}
              </Button>
              <Button
                variant="outlined"
                color="primary"
                fullWidth
                startIcon={<LogoutIcon />}
                onClick={() => {
                  handleLogout();
                  handleCloseNavMenu();
                }}
                sx={{
                  py: 1.2,
                  px: 3,
                  mb: 3,
                  borderRadius: '12px',
                  textTransform: 'none',
                  fontWeight: 600,
                  fontSize: '0.95rem',
                }}
              >
                {intl.formatMessage({ id: 'navbar.logout' })}
              </Button>
            </>
          ) : (
            <Button
              variant="outlined"
              color="primary"
              fullWidth
              startIcon={<LoginIcon />}
              onClick={() => {
                handleLogin();
                handleCloseNavMenu();
              }}
              sx={{
                py: 1.2,
                px: 3,
                mb: 3,
                borderRadius: '12px',
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '0.95rem',
              }}
            >
              {intl.formatMessage({ id: 'navbar.login' })}
            </Button>
          )}

          {/* Navigation Links */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 700,
                color: theme.palette.primary.main,
                mb: 2,
              }}
            >
              Navigasyon
            </Typography>

            <Button
              variant="outlined"
              color="primary"
              fullWidth
              onClick={() => {
                navigate('/test/brands');
                handleCloseNavMenu();
              }}
              sx={{
                py: 1.2,
                px: 3,
                mb: 2,
                borderRadius: '12px',
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '0.95rem',
              }}
            >
              {intl.formatMessage({ id: 'navbar.brands' })}
            </Button>
          </Box>

          {/* Categories List */}
          <Typography
            variant="h6"
            sx={{
              fontWeight: 700,
              color: theme.palette.primary.main,
              mb: 2,
            }}
          >
            {intl.formatMessage({ id: 'navbar.categories' })}
          </Typography>
          <List>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress size={24} />
              </Box>
            ) : error ? (
              <Typography color="error" align="center" sx={{ py: 2 }}>
                {error}
              </Typography>
            ) : (
              categoryTree.map((category) => renderCategoryItem(category, handleCloseNavMenu))
            )}
          </List>
        </Box>
      </Drawer>

      {/* Desktop Categories Menu */}
      <CategoryPopover
        id="category-menu"
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <CategoryList>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress size={24} />
            </Box>
          ) : error ? (
            <Typography color="error" align="center" sx={{ py: 2 }}>
              {error}
            </Typography>
          ) : (
            <Box>
              {categoryTree.map((category) => renderCategoryMenuItem(category))}
            </Box>
          )}
        </CategoryList>
      </CategoryPopover>

      {/* Quote Form Dialog */}
      <QuoteForm
        open={quoteFormOpen}
        onClose={handleQuoteFormClose}
        isNavbar={true}
      />
    </AppBar>
  );
};

export default Navbar;
