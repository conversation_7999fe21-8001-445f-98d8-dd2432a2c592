import React, { Suspense, lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { TestModeProvider } from '../common/TestModeWrapper';
import Box from '@mui/material/Box';
import { CircularProgress } from '@mui/material';
import Navbar from './Navbar';
import Footer from './Footer';
import ScrollToTop from './ScrollToTop';
import Chatbot from '../features/chatbot/Chatbot';
// Yükleme bileşeni
const LoadingComponent = () => (
  <Box sx={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '50vh'
  }}>
    <CircularProgress color="primary" />
  </Box>
);

// Yapım Aşamasındadır sayfası
const UnderConstruction = lazy(() => import('../../pages/UnderConstruction'));

// Ana uygulama sayfaları - lazy loading ile yükleme
const Home = lazy(() => import('../../pages/Home'));
const Categories = lazy(() => import('../../pages/Categories'));
const CategoryPage = lazy(() => import('../../pages/CategoryPage'));
const SearchResults = lazy(() => import('../../pages/SearchResults'));
const CampaignDetail = lazy(() => import('../../pages/CampaignDetail'));
const BrandPage = lazy(() => import('../../pages/BrandPage'));
const BrandsPage = lazy(() => import('../../pages/BrandsPage'));
const LoginPage = lazy(() => import('../../pages/LoginPage'));
const RegisterPage = lazy(() => import('../../pages/RegisterPage'));
const ProfilePage = lazy(() => import('../../pages/ProfilePage'));
const IlgilendigimKampanyalar = lazy(() => import('../../pages/IlgilendigimKampanyalar'));

// Test uygulaması için bir rota yönlendiricisi oluşturuyoruz
// Bu, test modunda navbar ve footer ile tam uygulama gösterir
const TestLayout: React.FC = () => {
  return (
    <TestModeProvider>
      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
        <ScrollToTop />
        <Navbar />
        <Box component="main" sx={{ flexGrow: 1 }}>
          <Suspense fallback={<LoadingComponent />}>
            <Routes>
              {/* Ana sayfa */}
              <Route path="/" element={<Home />} />

              {/* Arama sonuçları */}
              <Route path="/search" element={<SearchResults />} />

              {/* Giriş ve Kayıt sayfaları */}
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register" element={<RegisterPage />} />
              <Route path="/profile" element={<ProfilePage />} />
              <Route path="/my-campaigns" element={<IlgilendigimKampanyalar />} />

              {/* Kategori hiyerarşisi */}
              <Route path="/categories" element={<Categories />} />
              <Route path="/categories/:categoryId(\d+)" element={<Navigate to="/categories" replace />} />
              <Route path="/categories/:categoryName" element={<CategoryPage />} />

              {/* Marka hiyerarşisi */}
              <Route path="/marka" element={<BrandsPage />} />
              <Route path="/marka/:brandName" element={<BrandPage />} />
              <Route path="/marka/:brandName/:categorySlug" element={<BrandPage />} />
              <Route path="/marka/:brandName/campaigns/:campaignName" element={<CampaignDetail />} />

              {/* Kampanya hiyerarşisi */}
              <Route path="/campaigns/:campaignName" element={<CampaignDetail />} />

              {/* Eski ve ID içeren rotalar için yönlendirmeler */}
              {/* Kampanya Yönlendirmeleri */}
              <Route path="/campaigns/:id/:campaignName" element={<Navigate to="/campaigns/:campaignName" replace />} />
              <Route path="/campaign/:id" element={<Navigate to="/campaigns/:id" replace />} /> {/* Bu :id -> :campaignName olmalı */} 
              <Route path="/campaign/:id/:campaignName" element={<Navigate to="/campaigns/:campaignName" replace />} />

              {/* Eski brands URL'lerinden yeni marka URL'lerine yönlendirme */}
              <Route path="/brands" element={<Navigate to="/marka" replace />} />
              <Route path="/brands/:brandName" element={<Navigate to="/marka/:brandName" replace />} />
              <Route path="/brands/:brandName/:categorySlug" element={<Navigate to="/marka/:brandName/:categorySlug" replace />} />
              <Route path="/brands/:brandName/campaigns/:campaignName" element={<Navigate to="/marka/:brandName/campaigns/:campaignName" replace />} />

              {/* Marka Yönlendirmeleri */}
              <Route path="/brand/:brandId" element={<Navigate to="/marka/:brandId" replace />} />
              <Route path="/brand/:brandId/:brandName" element={<Navigate to="/marka/:brandName" replace />} />

              {/* Kategori Yönlendirmeleri (bunlar zaten ID ve isim alıyor, şimdilik dokunmuyorum) */}
              <Route path="/category/:categoryId" element={<Navigate to="/categories/:categoryId" replace />} />
              <Route path="/category/:categoryId/:categoryName" element={<Navigate to="/categories/:categoryName" replace />} />
              <Route path="/category-list/:groupId" element={<Navigate to="/categories/group/:groupId" replace />} />
            </Routes>
          </Suspense>
        </Box>
        <Footer />
        <Chatbot />
      </Box>
    </TestModeProvider>
  );
};

// Ana sayfa yönlendiricisi
const Layout: React.FC = () => {
  // Router kontrolleri

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <Suspense fallback={<LoadingComponent />}>
        <Routes>
          {/* Ana URL - Yapım Aşamasındadır */}
          <Route path="/" element={<UnderConstruction />} />

          {/* Login ve Register sayfaları */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          <Route path="/my-campaigns" element={<IlgilendigimKampanyalar />} />

          {/* /test rotası ve altındaki tüm yollar için test layout gösterilir */}
          <Route path="/test/*" element={<TestLayout />} />

          {/* Tanımlanmamış rotaları ana sayfaya yönlendir */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Suspense>
    </Box>
  );
};

export default Layout;