import React, { useState, useEffect } from 'react';
import { useTestMode } from '../../../components/common/TestModeWrapper';
import { getDaysLeft, parseDate } from '../../../utils/dateUtils';
import { createSlug } from '../../../utils/urlUtils';
import {
  Box,
  Paper,
  Typography,
  Button,
  useTheme,
} from '@mui/material';
import { useIntl } from 'react-intl';
import { alpha } from '@mui/material/styles';
import QuoteForm from '../quote/QuoteForm';

interface CampaignDetailField {
  type: 'text' | 'radio' | 'checkbox';
  label: string;
  value: string | boolean;
  options?: string[];
  required?: boolean;
}

interface CustomSlideProps {
  campaign: {
    id: string;
    title: string;
    brand: string;
    imageUrl: string;
    logoUrl?: string;
    brandId?: string; // Added brandId for direct brand linking
    brandUrl?: string; // Added brandUrl for direct brand linking
    endDate: string; // Kampanya bitiş tarihi
    category?: string; // Added category property for tracking
    features?: {
      price?: string;
      monthlyPayment?: string;
      term?: string;
      downPayment?: string;
      interestRate?: string;
    };
    details?: Record<string, CampaignDetailField>; // Dynamic campaign details
    description?: string; // Added description field
  };
}

export const CustomSlide: React.FC<CustomSlideProps> = ({ campaign }) => {
  const { navigate, isTestMode } = useTestMode();
  const intl = useIntl();
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const [brandLogoUrl, setBrandLogoUrl] = useState<string>('/placeholder-logo.jpg');
  const [quoteFormOpen, setQuoteFormOpen] = useState(false);

  useEffect(() => {
    if (campaign.brandId) {
      setBrandLogoUrl(`https://360avantajli.com/api/Campaign_Service/brand/${campaign.brandId}/image`);
    } else if (campaign.logoUrl) {
      setBrandLogoUrl(campaign.logoUrl);
    }
  }, [campaign.brandId, campaign.logoUrl]);

  // Saatleri hesapla (24 saatten az kalan kampanyalar için)
  const endDateObj = parseDate(campaign.endDate);
  const now = new Date();
  const hoursLeft = endDateObj ? Math.floor((endDateObj.getTime() - now.getTime()) / (1000 * 60 * 60)) : -1;
  const daysLeft = getDaysLeft(campaign.endDate);

  // 24 saatten az kalan veya bugün biten kampanyalar için kırmızı çerçeve
  const endingVeryVerySoon = hoursLeft >= 0 && hoursLeft < 24;
  const endsToday = daysLeft === 0;

  // Ana görsel endpointi
  const getShowcaseImageUrl = (campaignId: string) => {
    if (!campaignId) return '';
    return `https://360avantajli.com/api/Campaign_Service/campaign-image/showcase/${campaignId}`;
  };

  return (
    <Box sx={{ p: 1 }}>
      <Paper
        id={`custom-slide-${campaign.id}`}
        elevation={3}
        onClick={() => {
          // Kampanya detay sayfasına git
          const slug = createSlug(campaign.title);
          const url = isTestMode ? `/test/campaigns/${slug}` : `/campaigns/${slug}`;
          navigate(url);
        }}
        sx={{
          position: 'relative',
          borderRadius: 2,
          overflow: 'hidden',
          cursor: 'pointer',
          transition: 'transform 0.2s, box-shadow 0.3s',
          '&:hover': {
            transform: 'translateY(-4px)',
          },
          // 24 saatten az kalan veya bugün biten kampanyalar için kırmızı çerçeve
          ...(endingVeryVerySoon || endsToday ? {
            border: `3px solid ${alpha('#f44336', 0.8)}`,
            boxShadow: `0 4px 20px ${alpha('#f44336', 0.6)}`,
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%': { boxShadow: `0 4px 20px ${alpha('#f44336', 0.6)}` },
              '50%': { boxShadow: `0 4px 30px ${alpha('#f44336', 0.8)}` },
              '100%': { boxShadow: `0 4px 20px ${alpha('#f44336', 0.6)}` }
            }
          } : {}),
        }}
      >
        <Box
          component="img"
          src={getShowcaseImageUrl(campaign.id)}
          alt={campaign.title}
          sx={{
            width: '100%',
            height: 250, // 200'den 250'ye yükseltildi
            objectFit: 'cover',
            objectPosition: 'center', // Resmin merkez noktasını gösterir
          }}
        />

        {/* Brand logo and name */}
        <Box
          onClick={(e) => {
            e.stopPropagation();
            // Marka adını URL formatına dönüştür
            const brandName = campaign.brand.toLowerCase().replace(/\s+/g, '-');
            const brandRouteInfo = brandName;

            // Construct URL with /test prefix in test mode
            const fullBrandUrl = isTestMode
              ? `/test/marka/${brandRouteInfo}`
              : `/marka/${brandRouteInfo}`;

            // Use navigate instead of window.location
            navigate(fullBrandUrl);
          }}
          sx={{
            position: 'absolute',
            top: 12,
            left: 12,
            display: 'flex',
            alignItems: 'center',
            zIndex: 2,
            cursor: 'pointer',
            maxWidth: '70%',
          }}
        >
          {/* Logo */}
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: '50%',
              backgroundColor: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '4px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
              transition: 'all 0.2s ease',
              flexShrink: 0,
              '&:hover': {
                transform: 'scale(1.1)',
                boxShadow: '0 6px 16px rgba(0,0,0,0.25)',
              }
            }}
          >
            <img
              src={brandLogoUrl}
                  alt={`${campaign.brand} logo`}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                    borderRadius: '50%',
                  }}
                  onError={(e) => {
                    // Logo yüklenemezse varsayılan logo göster
                    const target = e.target as HTMLImageElement;
                    target.onerror = null; // Sonsuz döngüyü önle
                    target.src = '/placeholder-logo.jpg';
                  }}
                />
          </Box>
        </Box>

        {(() => {
          const daysLeft = getDaysLeft(campaign.endDate);

          // Renk belirleme
          let bgColor = theme.palette.primary.main;
          let text = '';

          if (daysLeft < 0) {
            // Süresi dolmuş kampanya - gösterme
            return null;
          } else if (daysLeft === 0) {
            // Bugün sona eriyor
            bgColor = theme.palette.error.main;
            text = intl.formatMessage({ id: 'campaign.endsToday' });
          } else if (daysLeft <= 3) {
            // 3 gün veya daha az kaldı
            bgColor = theme.palette.error.main;
            text = daysLeft === 1
              ? intl.formatMessage({ id: 'campaign.oneDayLeft' })
              : intl.formatMessage({ id: 'campaign.daysLeft.short' }, { days: daysLeft });
          } else if (daysLeft <= 7) {
            // 7 gün veya daha az kaldı
            bgColor = theme.palette.warning.main;
            text = intl.formatMessage({ id: 'campaign.daysLeft.short' }, { days: daysLeft });
          } else {
            // 7 günden fazla kaldı
            text = intl.formatMessage({ id: 'campaign.daysLeft.short' }, { days: daysLeft });
          }

          // 24 saatten az kalan kampanyalar için özel metin
          if (endingVeryVerySoon) {
            // 24 saatten az kaldığını belirt
            text = hoursLeft <= 1
              ? intl.formatMessage({ id: 'campaign.lessThanOneHour' }, { defaultMessage: 'Son 1 saat!' })
              : intl.formatMessage(
                  { id: 'campaign.hoursLeft' },
                  { hours: hoursLeft, defaultMessage: 'Son {hours} saat!' }
                );
          }

          return (
            <Box
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                bgcolor: bgColor,
                color: 'white',
                px: 1,
                py: 0.5,
                borderRadius: 1,
                fontSize: '0.75rem',
                fontWeight: 'bold',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                zIndex: 10,
                ...(endingVeryVerySoon && {
                  animation: 'blink 1s infinite',
                  '@keyframes blink': {
                    '0%': { opacity: 1 },
                    '50%': { opacity: 0.6 },
                    '100%': { opacity: 1 },
                  },
                }),
              }}
            >
              {text}
            </Box>
          );
        })()}
        <Box sx={{ p: 2 }}>

          <Typography
            variant="subtitle1"
            component="h3"
            sx={{
              fontWeight: 600,
              fontSize: '0.9rem',
              mb: 0.5,
              height: 24,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 1,
              WebkitBoxOrient: 'vertical',
            }}
          >
            {campaign.title}
          </Typography>
          {/* Kampanya açıklaması (description) */}
          {campaign.description && (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                mb: 1,
                minHeight: 80, // Yükseklik artırıldı
                maxHeight: 80, // Yükseklik artırıldı
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitLineClamp: 4, // 3'ten 4'e çıkarıldı
                WebkitBoxOrient: 'vertical',
                wordBreak: 'break-word',
                // Eğer metin kısaysa daha büyük font kullan
                fontSize: campaign.description && campaign.description.length < 100 ? '0.95rem' : '0.85rem',
                fontWeight: campaign.description && campaign.description.length < 100 ? 500 : 400,
                lineHeight: 1.4,
                hyphens: 'auto', // Gerektiğinde kelimeleri hecelemek için
              }}
            >
              {campaign.description}
            </Typography>
          )}
          <Button
            variant="contained"
            fullWidth
            onClick={(e) => {
              e.stopPropagation();
              setQuoteFormOpen(true);
            }}
            sx={{
              mt: 1,
              py: 1.2,
              borderRadius: '12px',
              textTransform: 'none',
              fontWeight: 600,
              fontSize: '0.95rem',
              boxShadow: isDarkMode
                ? 'none'
                : '0 8px 16px rgba(0, 0, 0, 0.1), inset 0 -2px 4px rgba(0, 0, 0, 0.05)',
              background: isDarkMode
                ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.9)}, ${alpha(theme.palette.primary.dark, 0.9)})`
                : `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
              backdropFilter: 'blur(8px)',
              color: '#fff',
              border: isDarkMode
                ? `1px solid ${alpha('#fff', 0.1)}`
                : 'none',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              '&:hover': {
                boxShadow: isDarkMode
                  ? 'none'
                  : '0 12px 24px rgba(0, 0, 0, 0.15), inset 0 -2px 6px rgba(0, 0, 0, 0.08)',
                transform: 'translateY(-2px)',
                background: isDarkMode
                  ? `linear-gradient(135deg, ${alpha(theme.palette.primary.dark, 0.9)}, ${alpha(theme.palette.primary.main, 0.9)})`
                  : `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
                border: isDarkMode
                  ? `1px solid ${alpha('#fff', 0.2)}`
                  : 'none',
              },
              '&:active': {
                transform: 'translateY(0)',
                boxShadow: isDarkMode
                  ? 'none'
                  : '0 4px 8px rgba(0, 0, 0, 0.1), inset 0 -1px 2px rgba(0, 0, 0, 0.05)',
              },
            }}
          >
            {intl.formatMessage({ id: 'campaign.getQuoteButton' })}
          </Button>
        </Box>
      </Paper>

      <QuoteForm
        open={quoteFormOpen}
        onClose={() => setQuoteFormOpen(false)}
        campaignDetails={{
          id: campaign.id,
          title: campaign.title,
          category: campaign.category || '',
          brand: campaign.brand,
          imageUrl: campaign.imageUrl
        }}
      />
    </Box>
  );
};
